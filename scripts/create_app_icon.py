#!/usr/bin/env python3
"""
NIRA-Tamil App Icon Generator
Creates professional app icons with Tamil script and brand colors
"""

import os
import sys
from PIL import Image, ImageDraw, ImageFont
import math

# NIRA Brand Colors
NIRA_PRIMARY = "#FF6B35"    # Vibrant Orange-Red
NIRA_SECONDARY = "#4ECDC4"  # Teal Blue
NIRA_ACCENT = "#FFE66D"     # Golden Yellow
WHITE = "#FFFFFF"
BLACK = "#1A1A1A"

def hex_to_rgb(hex_color):
    """Convert hex color to RGB tuple"""
    hex_color = hex_color.lstrip('#')
    return tuple(int(hex_color[i:i+2], 16) for i in (0, 2, 4))

def create_gradient_background(size, center_color, edge_color):
    """Create radial gradient background"""
    img = Image.new('RGB', (size, size))
    draw = ImageDraw.Draw(img)
    
    center = size // 2
    max_radius = center
    
    center_rgb = hex_to_rgb(center_color)
    edge_rgb = hex_to_rgb(edge_color)
    
    for y in range(size):
        for x in range(size):
            # Calculate distance from center
            distance = math.sqrt((x - center) ** 2 + (y - center) ** 2)
            
            # Normalize distance (0 to 1)
            if distance <= max_radius:
                ratio = distance / max_radius
            else:
                ratio = 1.0
            
            # Interpolate colors
            r = int(center_rgb[0] * (1 - ratio) + edge_rgb[0] * ratio)
            g = int(center_rgb[1] * (1 - ratio) + edge_rgb[1] * ratio)
            b = int(center_rgb[2] * (1 - ratio) + edge_rgb[2] * ratio)
            
            img.putpixel((x, y), (r, g, b))
    
    return img

def create_nira_icon(size=1024):
    """Create NIRA app icon with Tamil script"""
    
    # Create gradient background
    img = create_gradient_background(size, NIRA_SECONDARY, NIRA_PRIMARY)
    draw = ImageDraw.Draw(img)
    
    # Add golden border
    border_width = max(3, size // 128)
    border_color = hex_to_rgb(NIRA_ACCENT)
    
    # Draw border circle
    margin = border_width // 2
    draw.ellipse([margin, margin, size - margin, size - margin], 
                outline=border_color, width=border_width)
    
    # Add Tamil text "நீ" (NIRA)
    # For now, we'll use a placeholder since Tamil fonts might not be available
    # In production, you'd use a proper Tamil font
    
    # Calculate text size and position
    text = "நீ"  # Tamil script for NIRA
    text_size = size // 3  # Approximately 40% of icon size
    
    try:
        # Try to use a system font that supports Tamil
        font_paths = [
            "/System/Library/Fonts/Tamil Sangam MN.ttc",
            "/System/Library/Fonts/Noto Sans Tamil.ttf",
            "/usr/share/fonts/truetype/noto/NotoSansTamil-Regular.ttf",
        ]
        
        font = None
        for font_path in font_paths:
            if os.path.exists(font_path):
                try:
                    font = ImageFont.truetype(font_path, text_size)
                    break
                except:
                    continue
        
        if font is None:
            # Fallback to default font with English "NI"
            font = ImageFont.load_default()
            text = "NI"
            text_size = size // 4
    
    except Exception as e:
        print(f"Font loading error: {e}")
        # Use default font with fallback text
        font = ImageFont.load_default()
        text = "NI"
        text_size = size // 4
    
    # Get text dimensions
    bbox = draw.textbbox((0, 0), text, font=font)
    text_width = bbox[2] - bbox[0]
    text_height = bbox[3] - bbox[1]
    
    # Center the text
    x = (size - text_width) // 2
    y = (size - text_height) // 2
    
    # Add text shadow
    shadow_offset = max(2, size // 256)
    shadow_color = (0, 0, 0, 80)  # Semi-transparent black
    
    # Draw shadow
    draw.text((x + shadow_offset, y + shadow_offset), text, 
             fill=shadow_color, font=font)
    
    # Draw main text
    draw.text((x, y), text, fill=hex_to_rgb(WHITE), font=font)
    
    # Add subtle inner glow
    glow_margin = size // 12
    glow_color = (*hex_to_rgb(WHITE), 50)  # Semi-transparent white
    draw.ellipse([glow_margin, glow_margin, 
                 size - glow_margin, size - glow_margin], 
                outline=glow_color, width=1)
    
    return img

def create_all_icon_sizes():
    """Create all required iOS icon sizes"""
    
    # iOS icon sizes
    sizes = {
        "1024": 1024,  # App Store
        "180": 180,    # iPhone @3x
        "120": 120,    # iPhone @2x
        "167": 167,    # iPad Pro @2x
        "152": 152,    # iPad @2x
        "76": 76,      # iPad @1x
        "60": 60,      # iPhone @2x Spotlight
        "40": 40,      # iPhone @2x Spotlight
        "29": 29,      # Settings @2x
    }
    
    # Create output directory
    output_dir = "NIRA-Tamil/Assets.xcassets/AppIcon.appiconset"
    os.makedirs(output_dir, exist_ok=True)
    
    print("Creating NIRA-Tamil app icons...")
    
    for name, size in sizes.items():
        print(f"Creating {size}x{size} icon...")
        
        # Create icon
        icon = create_nira_icon(size)
        
        # Save icon
        filename = f"NIRA-Icon-{size}.png"
        filepath = os.path.join(output_dir, filename)
        icon.save(filepath, "PNG", quality=100)
        
        print(f"✅ Saved: {filename}")
    
    print("\n🎉 All NIRA-Tamil app icons created successfully!")
    print(f"📁 Icons saved to: {output_dir}")
    
    # Create web favicon
    print("\nCreating web favicon...")
    favicon = create_nira_icon(32)
    favicon.save("NIRA-Tamil/Assets.xcassets/NIRA-Brand-Assets/favicon.png", "PNG")
    print("✅ Favicon created: favicon.png")

def create_marketing_assets():
    """Create marketing and social media assets"""
    
    marketing_sizes = {
        "social_square": 1200,      # Instagram, Facebook posts
        "social_cover": (1200, 628), # Facebook cover
        "twitter_header": (1500, 500), # Twitter header
        "app_preview": 512,         # General marketing
    }
    
    output_dir = "NIRA-Tamil/Assets.xcassets/NIRA-Brand-Assets/Marketing"
    os.makedirs(output_dir, exist_ok=True)
    
    print("\nCreating marketing assets...")
    
    for name, size in marketing_sizes.items():
        if isinstance(size, tuple):
            # Rectangular assets
            width, height = size
            # Create with icon on left, text on right
            img = Image.new('RGB', (width, height), hex_to_rgb(NIRA_PRIMARY))
            
            # Add icon
            icon_size = min(width, height) // 2
            icon = create_nira_icon(icon_size)
            
            # Paste icon
            icon_x = height // 4
            icon_y = (height - icon_size) // 2
            img.paste(icon, (icon_x, icon_y))
            
            # Add text (simplified for now)
            draw = ImageDraw.Draw(img)
            text = "NIRA Tamil\nHeritage Language Learning"
            
            # Use default font for now
            font = ImageFont.load_default()
            
            # Position text
            text_x = icon_x + icon_size + 50
            text_y = height // 3
            
            draw.text((text_x, text_y), text, fill=hex_to_rgb(WHITE), font=font)
            
        else:
            # Square assets
            img = create_nira_icon(size)
        
        filename = f"NIRA-{name}.png"
        filepath = os.path.join(output_dir, filename)
        img.save(filepath, "PNG", quality=100)
        
        print(f"✅ Created: {filename}")

def update_contents_json():
    """Update Contents.json with proper filenames"""
    
    contents_json = """{
  "images" : [
    {
      "filename" : "NIRA-Icon-1024.png",
      "idiom" : "universal",
      "platform" : "ios",
      "size" : "1024x1024"
    },
    {
      "appearances" : [
        {
          "appearance" : "luminosity",
          "value" : "dark"
        }
      ],
      "filename" : "NIRA-Icon-1024.png",
      "idiom" : "universal",
      "platform" : "ios",
      "size" : "1024x1024"
    },
    {
      "appearances" : [
        {
          "appearance" : "luminosity",
          "value" : "tinted"
        }
      ],
      "filename" : "NIRA-Icon-1024.png",
      "idiom" : "universal",
      "platform" : "ios",
      "size" : "1024x1024"
    }
  ],
  "info" : {
    "author" : "xcode",
    "version" : 1
  }
}"""
    
    contents_path = "NIRA-Tamil/Assets.xcassets/AppIcon.appiconset/Contents.json"
    with open(contents_path, 'w') as f:
        f.write(contents_json)
    
    print("✅ Updated Contents.json")

if __name__ == "__main__":
    print("🎨 NIRA-Tamil Icon Generator")
    print("=" * 50)
    
    try:
        # Check if PIL is available
        import PIL
        print("✅ PIL (Pillow) is available")
        
        # Create all icons
        create_all_icon_sizes()
        
        # Create marketing assets
        create_marketing_assets()
        
        # Update Contents.json
        update_contents_json()
        
        print("\n🎉 NIRA-Tamil branding assets created successfully!")
        print("\n📋 Next steps:")
        print("1. Build your Xcode project to see the new icons")
        print("2. Test the icons on device/simulator")
        print("3. Use marketing assets for website and social media")
        print("4. Submit to App Store with professional branding!")
        
    except ImportError:
        print("❌ PIL (Pillow) not found. Install with:")
        print("pip install Pillow")
        sys.exit(1)
    
    except Exception as e:
        print(f"❌ Error: {e}")
        sys.exit(1)
