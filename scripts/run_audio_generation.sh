#!/bin/bash

# NIRA Tamil Literature Audio Generation Script
# This script generates Tamil audio for all literature content

echo "🚀 NIRA Tamil Literature Audio Generation"
echo "=========================================="

# Check if Python 3 is installed
if ! command -v python3 &> /dev/null; then
    echo "❌ Python 3 is required but not installed"
    exit 1
fi

# Check if pip is installed
if ! command -v pip3 &> /dev/null; then
    echo "❌ pip3 is required but not installed"
    exit 1
fi

# Navigate to script directory
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
cd "$SCRIPT_DIR"

echo "📁 Working directory: $SCRIPT_DIR"

# Install dependencies
echo "📦 Installing Python dependencies..."
pip3 install -r requirements.txt

# Check if Google TTS keys exist
KEYS_DIR="/Users/<USER>/Documents/NIRA-Tamil/googlettskeys"
if [ ! -d "$KEYS_DIR" ]; then
    echo "❌ Google TTS keys directory not found: $KEYS_DIR"
    exit 1
fi

KEY_COUNT=$(find "$KEYS_DIR" -name "*.json" | wc -l)
if [ "$KEY_COUNT" -eq 0 ]; then
    echo "❌ No Google TTS service account keys found in $KEYS_DIR"
    exit 1
fi

echo "✅ Found $KEY_COUNT Google TTS service account keys"

# Set Google Application Credentials to first available key
FIRST_KEY=$(find "$KEYS_DIR" -name "*.json" | head -1)
export GOOGLE_APPLICATION_CREDENTIALS="$FIRST_KEY"
echo "🔑 Using service account: $(basename "$FIRST_KEY")"

# Create output directory
OUTPUT_DIR="/Users/<USER>/Documents/NIRA-Tamil/generated_audio"
mkdir -p "$OUTPUT_DIR"
echo "📁 Audio output directory: $OUTPUT_DIR"

# Run the audio generation script
echo ""
echo "🎵 Starting audio generation..."
echo "⏳ This may take several minutes..."
echo ""

python3 generate_literature_audio.py

# Check exit status
if [ $? -eq 0 ]; then
    echo ""
    echo "🎉 Audio generation completed successfully!"
    echo "📁 Generated audio files are temporarily stored in: $OUTPUT_DIR"
    echo "☁️  Audio files have been uploaded to Supabase storage"
    echo "🗄️  Database has been updated with audio URLs"
else
    echo ""
    echo "❌ Audio generation failed!"
    echo "📋 Check the logs above for error details"
    exit 1
fi
