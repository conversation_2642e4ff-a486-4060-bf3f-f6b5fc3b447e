#!/usr/bin/env python3
"""
Test script for the new Reliable Panchang Service
This script tests the local calculation algorithms
"""

import math
from datetime import datetime, <PERSON><PERSON><PERSON>

def calculate_julian_day(date):
    """Calculate Julian Day Number"""
    year = date.year
    month = date.month
    day = date.day
    hour = date.hour
    minute = date.minute
    
    a = math.floor((14 - month) / 12)
    y = year + 4800 - a
    m = month + 12 * a - 3
    
    jdn = day + math.floor((153 * m + 2) / 5) + 365 * y + math.floor(y / 4) - math.floor(y / 100) + math.floor(y / 400) - 32045
    jd = jdn + (hour - 12) / 24 + minute / 1440
    
    return jd

def calculate_sun_position(julian_day):
    """Calculate Sun's position"""
    n = julian_day - 2451545.0
    L = (280.460 + 0.9856474 * n) % 360.0
    g = math.radians((357.528 + 0.9856003 * n) % 360.0)
    lambda_sun = (L + 1.915 * math.sin(g) + 0.020 * math.sin(2 * g)) % 360.0
    return lambda_sun

def calculate_moon_position(julian_day):
    """Calculate Moon's position"""
    n = julian_day - 2451545.0
    L = (218.316 + 13.176396 * n) % 360.0
    M = math.radians((134.963 + 13.064993 * n) % 360.0)
    F = math.radians((93.272 + 13.229350 * n) % 360.0)
    
    lambda_moon = (L + 6.289 * math.sin(M) + 1.274 * math.sin(2 * math.radians(L) - M) + 0.658 * math.sin(2 * math.radians(L))) % 360.0
    return lambda_moon

def calculate_tithi(sun_pos, moon_pos):
    """Calculate Tithi"""
    diff = (moon_pos - sun_pos + 360.0) % 360.0
    tithi_number = int(diff / 12.0) + 1
    
    tithi_names = [
        "Pratipad", "Dwitiya", "Tritiya", "Chaturthi", "Panchami",
        "Shashthi", "Saptami", "Ashtami", "Navami", "Dashami",
        "Ekadashi", "Dwadashi", "Trayodashi", "Chaturdashi", "Purnima/Amavasya"
    ]
    
    paksha = "Shukla" if tithi_number <= 15 else "Krishna"
    adjusted_number = tithi_number if tithi_number <= 15 else tithi_number - 15
    name = tithi_names[min(adjusted_number - 1, 14)]
    
    return {
        'number': adjusted_number,
        'name': name,
        'paksha': paksha
    }

def calculate_nakshatra(moon_pos):
    """Calculate Nakshatra"""
    nakshatra_number = int(moon_pos / 13.333333) + 1
    
    nakshatra_names = [
        "Ashwini", "Bharani", "Krittika", "Rohini", "Mrigashira",
        "Ardra", "Punarvasu", "Pushya", "Ashlesha", "Magha",
        "Purva Phalguni", "Uttara Phalguni", "Hasta", "Chitra", "Swati",
        "Vishakha", "Anuradha", "Jyeshtha", "Moola", "Purva Ashadha",
        "Uttara Ashadha", "Shravana", "Dhanishta", "Shatabhisha", "Purva Bhadrapada",
        "Uttara Bhadrapada", "Revati"
    ]
    
    lords = [
        "Ketu", "Venus", "Sun", "Moon", "Mars",
        "Rahu", "Jupiter", "Saturn", "Mercury", "Ketu",
        "Venus", "Sun", "Moon", "Mars", "Rahu",
        "Jupiter", "Saturn", "Mercury", "Ketu", "Venus",
        "Sun", "Moon", "Mars", "Rahu", "Jupiter",
        "Saturn", "Mercury"
    ]
    
    adjusted_number = min(nakshatra_number - 1, 26)
    return {
        'number': nakshatra_number,
        'name': nakshatra_names[adjusted_number],
        'lord': lords[adjusted_number]
    }

def calculate_yoga(sun_pos, moon_pos):
    """Calculate Yoga"""
    sum_pos = (sun_pos + moon_pos) % 360.0
    yoga_number = int(sum_pos / 13.333333) + 1
    
    yoga_names = [
        "Vishkumbha", "Priti", "Ayushman", "Saubhagya", "Shobhana",
        "Atiganda", "Sukarma", "Dhriti", "Shoola", "Ganda",
        "Vriddhi", "Dhruva", "Vyaghata", "Harshana", "Vajra",
        "Siddhi", "Vyatipata", "Variyan", "Parigha", "Shiva",
        "Siddha", "Sadhya", "Shubha", "Shukla", "Brahma",
        "Indra", "Vaidhriti"
    ]
    
    adjusted_number = min(yoga_number - 1, 26)
    return {
        'number': yoga_number,
        'name': yoga_names[adjusted_number]
    }

def calculate_karana(tithi_number):
    """Calculate Karana"""
    karana_names = [
        "Bava", "Balava", "Kaulava", "Taitila", "Gara",
        "Vanija", "Vishti", "Shakuni", "Chatushpada", "Naga", "Kimstughna"
    ]
    
    karana_number = (tithi_number - 1) % 7
    return {
        'number': karana_number + 1,
        'name': karana_names[karana_number]
    }

def test_panchang_calculation():
    """Test the panchang calculation for today"""
    print("🧪 Testing Local Panchang Calculation")
    print("=" * 50)
    
    # Test for today
    today = datetime.now()
    print(f"📅 Date: {today.strftime('%Y-%m-%d %H:%M:%S')}")
    
    # Calculate astronomical positions
    jd = calculate_julian_day(today)
    sun_pos = calculate_sun_position(jd)
    moon_pos = calculate_moon_position(jd)
    
    print(f"🌞 Sun Position: {sun_pos:.2f}°")
    print(f"🌙 Moon Position: {moon_pos:.2f}°")
    print()
    
    # Calculate panchang elements
    tithi = calculate_tithi(sun_pos, moon_pos)
    nakshatra = calculate_nakshatra(moon_pos)
    yoga = calculate_yoga(sun_pos, moon_pos)
    karana = calculate_karana(tithi['number'])
    
    # Display results
    print("🌙 Panchang Information:")
    print(f"   Tithi: {tithi['name']} ({tithi['number']}) - {tithi['paksha']} Paksha")
    print(f"   Nakshatra: {nakshatra['name']} ({nakshatra['number']}) - Lord: {nakshatra['lord']}")
    print(f"   Yoga: {yoga['name']} ({yoga['number']})")
    print(f"   Karana: {karana['name']} ({karana['number']})")
    print()
    
    print("✅ Local panchang calculation test completed successfully!")
    print()
    print("📝 Note: This matches the algorithm used in LocalPanchangCalculator.swift")
    print("🎯 The iOS app will use this same calculation method for reliable, offline panchang data")

def test_weekly_calculation():
    """Test calculation for a week"""
    print("\n🗓️ Testing Weekly Panchang Calculation")
    print("=" * 50)
    
    today = datetime.now()
    
    for i in range(7):
        test_date = today + timedelta(days=i)
        jd = calculate_julian_day(test_date)
        sun_pos = calculate_sun_position(jd)
        moon_pos = calculate_moon_position(jd)
        
        tithi = calculate_tithi(sun_pos, moon_pos)
        nakshatra = calculate_nakshatra(moon_pos)
        
        print(f"{test_date.strftime('%Y-%m-%d')}: {tithi['name']} | {nakshatra['name']}")
    
    print("\n✅ Weekly calculation test completed!")

if __name__ == "__main__":
    test_panchang_calculation()
    test_weekly_calculation()
