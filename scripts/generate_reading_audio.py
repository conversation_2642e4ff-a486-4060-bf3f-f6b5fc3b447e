#!/usr/bin/env python3
"""
Generate audio files for Tamil reading content using Google TTS
Uses multiple service account keys to avoid throttling
Uploads generated audio to Supabase storage
"""

import os
import json
import base64
import asyncio
import aiohttp
from pathlib import Path
from google.cloud import texttospeech
from supabase import create_client, Client
import logging

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Configuration
SUPABASE_URL = "https://wnsorhbsucjguaoquhvr.supabase.co"
SUPABASE_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************.HS2BW4qh6gySIUBz7fQJju0rUMGVz5ueNrW_lzk9hbs"
AUDIO_BUCKET = "audio"

# TTS Configuration - Approved Voices from memories
FEMALE_VOICE = "ta-IN-Chirp3-HD-Erinome"  # Premium Tamil Female
MALE_VOICE = "ta-IN-Chirp3-HD-Iapetus"    # Premium Tamil Male
LANGUAGE_CODE = "ta-IN"

# Service Account Keys Directory
KEYS_DIR = "/Users/<USER>/Documents/NIRA-Tamil/googlettskeys"

class ReadingAudioGenerator:
    def __init__(self):
        self.supabase: Client = create_client(SUPABASE_URL, SUPABASE_KEY)
        self.tts_client = None
        self.current_key_index = 0
        self.service_account_keys = []
        self.generated_count = 0
        
        # Setup Google TTS
        self.setup_google_tts()
        
    def setup_google_tts(self):
        """Setup Google Cloud TTS client with service account rotation"""
        google_keys_dir = Path(KEYS_DIR)
        
        if not google_keys_dir.exists():
            raise FileNotFoundError(f"Google TTS keys directory not found: {google_keys_dir}")
            
        key_files = list(google_keys_dir.glob("*.json"))
        if not key_files:
            raise FileNotFoundError("No Google TTS service account keys found")
            
        self.service_account_keys = key_files
        
        # Use first available key
        self.rotate_service_account()
        
    def rotate_service_account(self):
        """Rotate to next service account to avoid throttling"""
        if not self.service_account_keys:
            raise ValueError("No service account keys available")
            
        key_file = self.service_account_keys[self.current_key_index]
        os.environ['GOOGLE_APPLICATION_CREDENTIALS'] = str(key_file)
        
        self.tts_client = texttospeech.TextToSpeechClient()
        logger.info(f"✅ Rotated to service account: {key_file.name}")
        
        # Move to next key for next rotation
        self.current_key_index = (self.current_key_index + 1) % len(self.service_account_keys)
        
    async def generate_all_reading_audio(self):
        """Generate audio for all reading content"""
        logger.info("🎵 Starting reading audio generation...")
        
        # Fetch reading content from Supabase
        reading_content = await self.fetch_reading_content()
        logger.info(f"📖 Found {len(reading_content)} reading content items")
        
        for i, content in enumerate(reading_content):
            try:
                # Skip if audio already exists
                if content.get('audio_url'):
                    logger.info(f"⏭️  Skipping {content['title_english']} - audio already exists")
                    continue
                
                # Generate audio
                audio_url = await self.generate_content_audio(content)
                
                # Update database with audio URL
                await self.update_content_audio_url(content['content_id'], audio_url)
                
                self.generated_count += 1
                logger.info(f"✅ Generated audio {i+1}/{len(reading_content)}: {content['title_english']}")
                
                # Small delay to avoid rate limiting
                await asyncio.sleep(0.5)
                
                # Rotate service account every 50 requests
                if self.generated_count % 50 == 0:
                    self.rotate_service_account()
                    
            except Exception as e:
                logger.error(f"❌ Failed to generate audio for {content['title_english']}: {e}")
                continue
                
        logger.info(f"🎉 Audio generation completed! Generated {self.generated_count} files.")
        
    async def fetch_reading_content(self):
        """Fetch reading content from Supabase"""
        try:
            response = self.supabase.table('reading_content').select('*').eq('is_active', True).execute()
            return response.data
        except Exception as e:
            logger.error(f"Failed to fetch reading content: {e}")
            return []
            
    async def generate_content_audio(self, content):
        """Generate audio for a single reading content item"""
        text = content['content_tamil']
        content_id = content['content_id']
        
        # Determine voice (use female as default)
        voice = FEMALE_VOICE
        
        # Generate audio using Google TTS
        audio_data = await self.generate_tts_audio(text, voice)
        
        # Upload to Supabase Storage
        filename = f"reading_{content_id}.mp3"
        audio_url = await self.upload_to_supabase(audio_data, filename)
        
        return audio_url
        
    async def generate_tts_audio(self, text, voice):
        """Generate audio using Google Cloud TTS"""
        try:
            # Synthesis input
            synthesis_input = texttospeech.SynthesisInput(text=text)
            
            # Voice selection
            voice_params = texttospeech.VoiceSelectionParams(
                language_code=LANGUAGE_CODE,
                name=voice
            )
            
            # Audio config
            audio_config = texttospeech.AudioConfig(
                audio_encoding=texttospeech.AudioEncoding.MP3,
                speaking_rate=0.9,  # Slightly slower for learning
                pitch=0.0
            )
            
            # Perform the text-to-speech request
            response = self.tts_client.synthesize_speech(
                input=synthesis_input,
                voice=voice_params,
                audio_config=audio_config
            )
            
            return response.audio_content
            
        except Exception as e:
            logger.error(f"TTS generation failed: {e}")
            # Rotate service account and retry once
            self.rotate_service_account()
            
            response = self.tts_client.synthesize_speech(
                input=synthesis_input,
                voice=voice_params,
                audio_config=audio_config
            )
            
            return response.audio_content
            
    async def upload_to_supabase(self, audio_data, filename):
        """Upload audio file to Supabase Storage"""
        try:
            file_path = f"reading_content/{filename}"
            
            # Upload file
            self.supabase.storage.from_(AUDIO_BUCKET).upload(
                file_path, 
                audio_data,
                file_options={"content-type": "audio/mpeg"}
            )
            
            # Get public URL
            public_url = self.supabase.storage.from_(AUDIO_BUCKET).get_public_url(file_path)
            
            logger.info(f"📤 Uploaded audio: {filename}")
            return public_url
            
        except Exception as e:
            logger.error(f"Failed to upload {filename}: {e}")
            raise
            
    async def update_content_audio_url(self, content_id, audio_url):
        """Update reading content with audio URL"""
        try:
            self.supabase.table('reading_content').update({
                'audio_url': audio_url
            }).eq('content_id', content_id).execute()
            
        except Exception as e:
            logger.error(f"Failed to update audio URL for {content_id}: {e}")
            raise

async def main():
    """Main function"""
    try:
        generator = ReadingAudioGenerator()
        await generator.generate_all_reading_audio()
        
    except Exception as e:
        logger.error(f"Script failed: {e}")
        return 1
        
    return 0

if __name__ == "__main__":
    import sys
    sys.exit(asyncio.run(main()))
