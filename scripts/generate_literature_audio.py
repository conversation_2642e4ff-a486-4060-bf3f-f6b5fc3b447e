#!/usr/bin/env python3
"""
Literature Audio Generation Script
Generates Tamil audio for all literature content using Google TTS
Downloads locally and uploads to Supabase storage
"""

import os
import json
import asyncio
import aiohttp
import base64
from pathlib import Path
from typing import List, Dict, Optional
import uuid
from supabase import create_client, Client
from google.cloud import texttospeech
from google.oauth2 import service_account
import time

# Configuration
SUPABASE_URL = "https://wnsorhbsucjguaoquhvr.supabase.co"
SUPABASE_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************.HS2BW4qh6gySIUBz7fQJju0rUMGVz5ueNrW_lzk9hbs"

# Google TTS Configuration
TAMIL_FEMALE_VOICE = "ta-IN-Chirp3-HD-Erinome"
TAMIL_MALE_VOICE = "ta-IN-Chirp3-HD-Iapetus"
LANGUAGE_CODE = "ta-IN"
AUDIO_FORMAT = texttospeech.AudioEncoding.MP3
SPEAKING_RATE = 0.9
PITCH = 0.0

# Paths
KEYS_DIR = Path("/Users/<USER>/Documents/NIRA-Tamil/googlettskeys")
AUDIO_OUTPUT_DIR = Path("/Users/<USER>/Documents/NIRA-Tamil/generated_audio")
SCRIPTS_DIR = Path(__file__).parent

class LiteratureAudioGenerator:
    def __init__(self):
        self.supabase: Client = create_client(SUPABASE_URL, SUPABASE_KEY)
        self.service_accounts = []
        self.current_account_index = 0
        self.generated_count = 0
        self.total_count = 0

        # Create output directory
        AUDIO_OUTPUT_DIR.mkdir(exist_ok=True)

        # Load service accounts
        self.load_service_accounts()

    def load_service_accounts(self):
        """Load all Google TTS service accounts"""
        try:
            json_files = list(KEYS_DIR.glob("*.json"))
            for json_file in json_files:
                with open(json_file, 'r') as f:
                    service_account_info = json.load(f)
                    self.service_accounts.append(service_account_info)

            print(f"✅ Loaded {len(self.service_accounts)} service accounts")

        except Exception as e:
            print(f"❌ Failed to load service accounts: {e}")
            raise

    def get_current_tts_client(self):
        """Get current Google TTS client with rotation"""
        if not self.service_accounts:
            raise Exception("No service accounts available")

        service_account_info = self.service_accounts[self.current_account_index]
        credentials = service_account.Credentials.from_service_account_info(service_account_info)
        return texttospeech.TextToSpeechClient(credentials=credentials)

    def rotate_service_account(self):
        """Rotate to next service account"""
        self.current_account_index = (self.current_account_index + 1) % len(self.service_accounts)
        project_id = self.service_accounts[self.current_account_index].get('project_id', 'unknown')
        print(f"🔄 Rotated to service account: {project_id}")

    async def fetch_literature_content(self) -> List[Dict]:
        """Fetch all literature content from Supabase"""
        try:
            response = self.supabase.table("literature_content").select("*").execute()
            return response.data
        except Exception as e:
            print(f"❌ Failed to fetch literature content: {e}")
            raise

    def generate_audio_file(self, text: str, voice_name: str, output_path: Path) -> bool:
        """Generate audio file using Google TTS"""
        max_retries = len(self.service_accounts)

        for attempt in range(max_retries):
            try:
                client = self.get_current_tts_client()

                # Set up the synthesis input
                synthesis_input = texttospeech.SynthesisInput(text=text)

                # Build the voice request
                voice = texttospeech.VoiceSelectionParams(
                    language_code=LANGUAGE_CODE,
                    name=voice_name
                )

                # Select the type of audio file
                audio_config = texttospeech.AudioConfig(
                    audio_encoding=AUDIO_FORMAT,
                    speaking_rate=SPEAKING_RATE,
                    pitch=PITCH
                )

                # Perform the text-to-speech request
                response = client.synthesize_speech(
                    input=synthesis_input,
                    voice=voice,
                    audio_config=audio_config
                )

                # Write the response to the output file
                with open(output_path, "wb") as out:
                    out.write(response.audio_content)

                print(f"✅ Generated: {output_path.name}")
                return True

            except Exception as e:
                print(f"❌ Attempt {attempt + 1} failed: {e}")
                if "quota" in str(e).lower() or "limit" in str(e).lower():
                    self.rotate_service_account()
                    time.sleep(2)  # Wait before retry
                else:
                    break

        print(f"❌ Failed to generate audio after {max_retries} attempts")
        return False

    async def upload_to_supabase(self, file_path: Path, bucket_name: str = "audio") -> Optional[str]:
        """Upload audio file to Supabase storage"""
        try:
            # Read file
            with open(file_path, 'rb') as f:
                file_data = f.read()

            # Upload to Supabase storage
            storage_path = f"literature/{file_path.name}"

            # Upload file
            self.supabase.storage.from_(bucket_name).upload(
                path=storage_path,
                file=file_data,
                file_options={"content-type": "audio/mpeg"}
            )

            # Get public URL
            public_url = self.supabase.storage.from_(bucket_name).get_public_url(storage_path)

            print(f"✅ Uploaded: {storage_path}")
            return public_url

        except Exception as e:
            print(f"❌ Failed to upload {file_path.name}: {e}")
            return None

    async def update_literature_audio_urls(self, content_id: str, female_url: str, male_url: str):
        """Update literature content with audio URLs"""
        try:
            self.supabase.table("literature_content").update({
                "audio_female_url": female_url,
                "audio_male_url": male_url,
                "audio_url": female_url  # Default to female voice
            }).eq("id", content_id).execute()

            print(f"✅ Updated audio URLs for content: {content_id}")

        except Exception as e:
            print(f"❌ Failed to update audio URLs for {content_id}: {e}")

    async def process_literature_item(self, content: Dict) -> bool:
        """Process a single literature item"""
        content_id = content['id']
        title = content['title']
        tamil_text = content['content_tamil']

        print(f"\n📚 Processing: {title}")
        print(f"🆔 ID: {content_id}")

        # Generate filenames
        female_filename = f"literature_{content_id}_female.mp3"
        male_filename = f"literature_{content_id}_male.mp3"

        female_path = AUDIO_OUTPUT_DIR / female_filename
        male_path = AUDIO_OUTPUT_DIR / male_filename

        # Generate audio files
        female_success = self.generate_audio_file(tamil_text, TAMIL_FEMALE_VOICE, female_path)
        if not female_success:
            return False

        # Small delay between requests
        time.sleep(1)

        male_success = self.generate_audio_file(tamil_text, TAMIL_MALE_VOICE, male_path)
        if not male_success:
            return False

        # Upload to Supabase
        female_url = await self.upload_to_supabase(female_path)
        male_url = await self.upload_to_supabase(male_path)

        if female_url and male_url:
            # Update database
            await self.update_literature_audio_urls(content_id, female_url, male_url)

            # Clean up local files
            female_path.unlink()
            male_path.unlink()

            self.generated_count += 2
            return True

        return False

    async def generate_all_literature_audio(self):
        """Generate audio for all literature content"""
        print("🎵 Starting Literature Audio Generation")
        print("=" * 50)

        # Fetch all literature content
        literature_content = await self.fetch_literature_content()
        self.total_count = len(literature_content) * 2  # Both male and female voices

        print(f"📚 Found {len(literature_content)} literature items")
        print(f"🎯 Will generate {self.total_count} audio files")
        print()

        successful_items = 0
        failed_items = 0

        # Process each literature item
        for i, content in enumerate(literature_content, 1):
            print(f"📖 Processing item {i}/{len(literature_content)}")

            try:
                success = await self.process_literature_item(content)
                if success:
                    successful_items += 1
                    print(f"✅ Success! Progress: {self.generated_count}/{self.total_count}")
                else:
                    failed_items += 1
                    print(f"❌ Failed to process: {content['title']}")

                # Progress update
                progress = (i / len(literature_content)) * 100
                print(f"📊 Overall Progress: {progress:.1f}%")

                # Delay between items to respect API limits
                if i < len(literature_content):
                    print("⏳ Waiting 3 seconds before next item...")
                    time.sleep(3)

            except Exception as e:
                failed_items += 1
                print(f"❌ Error processing {content['title']}: {e}")

        # Final summary
        print("\n" + "=" * 50)
        print("🎵 Literature Audio Generation Complete!")
        print(f"✅ Successful items: {successful_items}")
        print(f"❌ Failed items: {failed_items}")
        print(f"🎯 Total audio files generated: {self.generated_count}")
        print(f"📊 Success rate: {(successful_items / len(literature_content) * 100):.1f}%")

        if failed_items > 0:
            print(f"\n⚠️  {failed_items} items failed. Check logs above for details.")
        else:
            print("\n🎉 All literature items processed successfully!")

async def main():
    """Main execution function"""
    try:
        generator = LiteratureAudioGenerator()
        await generator.generate_all_literature_audio()

    except KeyboardInterrupt:
        print("\n⏹️  Generation stopped by user")
    except Exception as e:
        print(f"\n❌ Fatal error: {e}")
        raise

if __name__ == "__main__":
    print("🚀 NIRA Tamil Literature Audio Generator")
    print("🎵 Generating Tamil audio using Google TTS Premium voices")
    print("👩 Female Voice: ta-IN-Chirp3-HD-Erinome")
    print("👨 Male Voice: ta-IN-Chirp3-HD-Iapetus")
    print()

    # Run the async main function
    asyncio.run(main())