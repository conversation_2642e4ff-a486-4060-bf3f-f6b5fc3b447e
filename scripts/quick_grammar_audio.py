#!/usr/bin/env python3
"""
Quick Grammar Audio Generation Script
Simple script to generate audio for grammar examples and update database
"""

import asyncio
import aiohttp
import json
import os
import ssl
from google.cloud import texttospeech
from google.oauth2 import service_account

# Configuration
SUPABASE_URL = "https://wnsorhbsucjguaoquhvr.supabase.co"
SUPABASE_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.******************************************************************************************************************************************.huEg6PWHSgAaXAtREOLf1aRa3OENbOb437b8lEGyubc"  # Service role key
AUDIO_BUCKET = "audio"
LESSON_ID = "7b8c60af-dd2f-4754-9363-ab09a5bcea95"

# TTS Configuration
FEMALE_VOICE = "ta-IN-Chirp3-HD-Erinome"
LANGUAGE_CODE = "ta-IN"

# Service Account
SERVICE_ACCOUNT_PATH = "/Users/<USER>/Documents/NIRA-Tamil/googlettskeys/nira-460718-7e3f3c2b36fa.json"

# Grammar examples data (from our previous query)
GRAMMAR_EXAMPLES = [
    {
        "id": "d9b80d40-bb5f-40e4-9984-f3f16ae42826",
        "example_tamil": "காலை வணக்கம்!",
        "example_english": "Good morning!",
        "filename": "lesson_01_grammar_01_example_01.mp3"
    },
    {
        "id": "55fdd9b2-c32c-4cef-8a9d-0bb1a9a752e4",
        "example_tamil": "மாலை வணக்கம்!",
        "example_english": "Good evening!",
        "filename": "lesson_01_grammar_01_example_02.mp3"
    },
    {
        "id": "2ec5a058-8b46-4587-bc33-604a29d844d3",
        "example_tamil": "இராத்திரி வணக்கம்!",
        "example_english": "Good night!",
        "filename": "lesson_01_grammar_01_example_03.mp3"
    },
    {
        "id": "a71808d0-5a33-4e88-abd5-b5753dfe808d",
        "example_tamil": "வணக்கம்!",
        "example_english": "Hello!",
        "filename": "lesson_01_grammar_01_example_04.mp3"
    },
    {
        "id": "e046bcdb-3152-4dbe-916d-8fa4e1a6435a",
        "example_tamil": "எப்படி இருக்கிறீர்கள்?",
        "example_english": "How are you?",
        "filename": "lesson_01_grammar_01_example_05.mp3"
    },
    {
        "id": "fee12d11-c22b-4cd0-929c-0959f4d1ceef",
        "example_tamil": "நலமாக இருக்கிறேன், நன்றி.",
        "example_english": "I am fine, thank you.",
        "filename": "lesson_01_grammar_01_example_06.mp3"
    },
    {
        "id": "7394229b-46e2-4795-9be6-13ae118e7aa5",
        "example_tamil": "போகிறேன்",
        "example_english": "I am going (implied subject)",
        "filename": "lesson_01_grammar_01_example_07.mp3"
    },
    {
        "id": "38a8d8eb-e7fd-44a9-8ccd-0ecc29d1f674",
        "example_tamil": "வருகிறேன்",
        "example_english": "I am coming (implied subject)",
        "filename": "lesson_01_grammar_01_example_08.mp3"
    },
    {
        "id": "2d58f3f9-dd96-4f12-801e-565d8faae21d",
        "example_tamil": "பின்னால் சந்திக்கலாம்",
        "example_english": "See you later (implied subject)",
        "filename": "lesson_01_grammar_01_example_09.mp3"
    },
    {
        "id": "3bf15b5f-05f6-4df8-a7b7-13111d0006a8",
        "example_tamil": "ஹாய்!",
        "example_english": "Hi!",
        "filename": "lesson_01_grammar_02_example_01.mp3"
    }
]

class QuickGrammarAudioGenerator:
    def __init__(self):
        self.client = self.setup_tts_client()
        
    def setup_tts_client(self):
        """Setup Google TTS client"""
        credentials = service_account.Credentials.from_service_account_file(SERVICE_ACCOUNT_PATH)
        return texttospeech.TextToSpeechClient(credentials=credentials)
    
    def generate_audio(self, text, filename):
        """Generate audio using Google TTS"""
        print(f"🎵 Generating audio for: {text}")
        
        # Set up the request
        synthesis_input = texttospeech.SynthesisInput(text=text)
        voice = texttospeech.VoiceSelectionParams(
            language_code=LANGUAGE_CODE,
            name=FEMALE_VOICE
        )
        audio_config = texttospeech.AudioConfig(
            audio_encoding=texttospeech.AudioEncoding.MP3,
            speaking_rate=0.9,
            pitch=0.0
        )
        
        # Perform the text-to-speech request
        response = self.client.synthesize_speech(
            input=synthesis_input,
            voice=voice,
            audio_config=audio_config
        )
        
        print(f"✅ Generated {len(response.audio_content)} bytes for {filename}")
        return response.audio_content
    
    async def upload_to_supabase(self, audio_data, filename):
        """Upload audio to Supabase Storage"""
        headers = {
            "Authorization": f"Bearer {SUPABASE_KEY}",
            "apikey": SUPABASE_KEY,
            "Content-Type": "audio/mpeg"
        }

        url = f"{SUPABASE_URL}/storage/v1/object/{AUDIO_BUCKET}/{filename}"

        # Create SSL context that doesn't verify certificates (for development)
        ssl_context = ssl.create_default_context()
        ssl_context.check_hostname = False
        ssl_context.verify_mode = ssl.CERT_NONE

        connector = aiohttp.TCPConnector(ssl=ssl_context)

        async with aiohttp.ClientSession(connector=connector) as session:
            async with session.post(url, headers=headers, data=audio_data) as response:
                if response.status in [200, 201]:
                    public_url = f"{SUPABASE_URL}/storage/v1/object/public/{AUDIO_BUCKET}/{filename}"
                    print(f"📤 Uploaded: {filename}")
                    return public_url
                else:
                    error_text = await response.text()
                    print(f"❌ Upload failed for {filename}: {response.status} - {error_text}")
                    return None
    
    async def update_database(self, example_id, audio_url):
        """Update grammar example with audio URL"""
        headers = {
            "Authorization": f"Bearer {SUPABASE_KEY}",
            "apikey": SUPABASE_KEY,
            "Content-Type": "application/json",
            "Prefer": "return=minimal"
        }

        url = f"{SUPABASE_URL}/rest/v1/grammar_examples"
        params = {"id": f"eq.{example_id}"}
        data = {"audio_url": audio_url}

        # Create SSL context that doesn't verify certificates (for development)
        ssl_context = ssl.create_default_context()
        ssl_context.check_hostname = False
        ssl_context.verify_mode = ssl.CERT_NONE

        connector = aiohttp.TCPConnector(ssl=ssl_context)

        async with aiohttp.ClientSession(connector=connector) as session:
            async with session.patch(url, headers=headers, params=params, json=data) as response:
                if response.status in [200, 204]:
                    print(f"💾 Updated database for example {example_id}")
                    return True
                else:
                    error_text = await response.text()
                    print(f"❌ Database update failed for {example_id}: {response.status} - {error_text}")
                    return False
    
    async def process_all_examples(self):
        """Process all grammar examples"""
        print("🚀 Starting grammar audio generation...")
        print(f"📊 Processing {len(GRAMMAR_EXAMPLES)} examples")
        
        success_count = 0
        
        for i, example in enumerate(GRAMMAR_EXAMPLES, 1):
            try:
                print(f"\n📝 Processing {i}/{len(GRAMMAR_EXAMPLES)}: {example['example_english']}")
                
                # Generate audio
                audio_data = self.generate_audio(example['example_tamil'], example['filename'])
                
                # Upload to Supabase
                public_url = await self.upload_to_supabase(audio_data, example['filename'])
                
                if public_url:
                    # Update database
                    success = await self.update_database(example['id'], public_url)
                    if success:
                        success_count += 1
                        print(f"✅ Completed: {example['filename']}")
                    else:
                        print(f"⚠️  Upload succeeded but database update failed for {example['filename']}")
                else:
                    print(f"❌ Failed to upload {example['filename']}")
                
                # Small delay to avoid overwhelming services
                await asyncio.sleep(1)
                
            except Exception as e:
                print(f"❌ Error processing {example['filename']}: {e}")
                continue
        
        print(f"\n🎉 Completed! Successfully processed {success_count}/{len(GRAMMAR_EXAMPLES)} examples")
        
        if success_count > 0:
            print("\n🔊 Audio files are now available in the app!")
            print("📱 Test the grammar examples in the lesson to hear the audio.")

async def main():
    """Main function"""
    if not os.path.exists(SERVICE_ACCOUNT_PATH):
        print(f"❌ Service account file not found: {SERVICE_ACCOUNT_PATH}")
        return
    
    generator = QuickGrammarAudioGenerator()
    await generator.process_all_examples()

if __name__ == "__main__":
    print("🎵 Quick Grammar Audio Generator")
    print("=" * 50)
    asyncio.run(main())
