#!/usr/bin/env python3
"""
Literature Audio Upload Script
Uploads generated Tamil audio files to Supabase storage and updates database
"""

import os
import asyncio
from pathlib import Path
from typing import List, Dict, Tuple
from supabase import create_client, Client
import time

# Configuration
SUPABASE_URL = "https://wnsorhbsucjguaoquhvr.supabase.co"
SUPABASE_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Induc29yaGJzdWNqZ3Vhb3F1aHZyIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTAyNzUzOTgsImV4cCI6MjA2NTg1MTM5OH0.HS2BW4qh6gySIUBz7fQJju0rUMGVz5ueNrW_lzk9hbs"

# Paths
AUDIO_DIR = Path("/Users/<USER>/Documents/NIRA-Tamil/literature_audio")
BUCKET_NAME = "audio"

class LiteratureAudioUploader:
    def __init__(self):
        self.supabase: Client = create_client(SUPABASE_URL, SUPABASE_KEY)
        self.uploaded_count = 0
        self.total_files = 0
        self.failed_uploads = []
        
    def get_audio_files(self) -> List[Tuple[Path, str, str]]:
        """Get all audio files and extract content IDs and voice types"""
        audio_files = []

        for file_path in AUDIO_DIR.glob("*.mp3"):
            filename = file_path.name

            # Extract content ID and voice type from filename
            # Format: literature_{content_id}_{voice_type}.mp3
            if filename.startswith("literature_") and filename.endswith(".mp3"):
                # Remove "literature_" prefix and ".mp3" suffix
                core_name = filename[11:-4]

                # Split by underscore and find voice type (last part)
                if core_name.endswith("_female"):
                    voice_type = "female"
                    content_id = core_name[:-7]  # Remove "_female"
                elif core_name.endswith("_male"):
                    voice_type = "male"
                    content_id = core_name[:-5]  # Remove "_male"
                else:
                    continue  # Skip files that don't match expected pattern

                audio_files.append((file_path, content_id, voice_type))
                print(f"📁 Found: {filename} -> ID: {content_id}, Voice: {voice_type}")

        return audio_files
    
    def create_storage_bucket(self):
        """Create audio storage bucket if it doesn't exist"""
        try:
            # Try to create bucket
            self.supabase.storage.create_bucket(BUCKET_NAME, {"public": True})
            print(f"✅ Created storage bucket: {BUCKET_NAME}")
        except Exception as e:
            if "already exists" in str(e).lower():
                print(f"✅ Storage bucket already exists: {BUCKET_NAME}")
            else:
                print(f"⚠️  Bucket creation warning: {e}")
    
    def upload_audio_file(self, file_path: Path, storage_path: str) -> str:
        """Upload a single audio file to Supabase storage"""
        try:
            # Read file
            with open(file_path, 'rb') as f:
                file_data = f.read()
            
            # Upload to Supabase storage
            self.supabase.storage.from_(BUCKET_NAME).upload(
                storage_path,
                file_data,
                file_options={"content-type": "audio/mpeg"}
            )
            
            # Get public URL
            public_url = self.supabase.storage.from_(BUCKET_NAME).get_public_url(storage_path)
            
            print(f"✅ Uploaded: {storage_path}")
            return public_url
            
        except Exception as e:
            print(f"❌ Failed to upload {file_path.name}: {e}")
            raise
    
    def update_database_audio_urls(self, content_id: str, female_url: str, male_url: str):
        """Update literature content with audio URLs"""
        try:
            self.supabase.table("literature_content").update({
                "audio_female_url": female_url,
                "audio_male_url": male_url,
                "audio_url": female_url  # Default to female voice
            }).eq("id", content_id).execute()
            
            print(f"✅ Updated database for content: {content_id}")
            
        except Exception as e:
            print(f"❌ Failed to update database for {content_id}: {e}")
            raise
    
    async def upload_all_audio(self):
        """Upload all audio files and update database"""
        print("🎵 Starting Literature Audio Upload")
        print("=" * 60)
        
        # Create storage bucket
        self.create_storage_bucket()
        
        # Get all audio files
        audio_files = self.get_audio_files()
        self.total_files = len(audio_files)
        
        print(f"📁 Found {self.total_files} audio files to upload")
        print(f"📁 Source directory: {AUDIO_DIR}")
        print()
        
        # Group files by content ID
        content_audio = {}
        for file_path, content_id, voice_type in audio_files:
            if content_id not in content_audio:
                content_audio[content_id] = {}
            content_audio[content_id][voice_type] = file_path
        
        print(f"📚 Processing {len(content_audio)} literature items")
        print()
        
        successful_items = 0
        failed_items = 0
        
        # Process each content item
        for i, (content_id, voice_files) in enumerate(content_audio.items(), 1):
            print(f"📖 Processing item {i}/{len(content_audio)}")
            print(f"🆔 Content ID: {content_id}")
            
            try:
                female_url = None
                male_url = None
                
                # Upload female voice if available
                if "female" in voice_files:
                    female_path = voice_files["female"]
                    storage_path = f"literature/{female_path.name}"
                    female_url = self.upload_audio_file(female_path, storage_path)
                    self.uploaded_count += 1
                
                # Upload male voice if available
                if "male" in voice_files:
                    male_path = voice_files["male"]
                    storage_path = f"literature/{male_path.name}"
                    male_url = self.upload_audio_file(male_path, storage_path)
                    self.uploaded_count += 1
                
                # Update database if we have at least one URL
                if female_url or male_url:
                    self.update_database_audio_urls(
                        content_id, 
                        female_url or "", 
                        male_url or ""
                    )
                    successful_items += 1
                    print(f"✅ Success! Uploaded: {self.uploaded_count}/{self.total_files}")
                else:
                    failed_items += 1
                    print(f"❌ No audio files found for content: {content_id}")
                
                # Progress update
                progress = (i / len(content_audio)) * 100
                print(f"📊 Overall Progress: {progress:.1f}%")
                print()
                
                # Small delay between uploads
                if i < len(content_audio):
                    time.sleep(0.5)
                
            except Exception as e:
                failed_items += 1
                self.failed_uploads.append((content_id, str(e)))
                print(f"❌ Error processing {content_id}: {e}")
                print()
        
        # Final summary
        print("=" * 60)
        print("🎵 Literature Audio Upload Complete!")
        print(f"✅ Successful items: {successful_items}")
        print(f"❌ Failed items: {failed_items}")
        print(f"📁 Total files uploaded: {self.uploaded_count}")
        if len(content_audio) > 0:
            print(f"📊 Success rate: {(successful_items / len(content_audio) * 100):.1f}%")
        else:
            print("📊 Success rate: 0.0% (no files found)")
        
        if self.failed_uploads:
            print(f"\n⚠️  Failed uploads:")
            for content_id, error in self.failed_uploads:
                print(f"   - {content_id}: {error}")
        else:
            print("\n🎉 All audio files uploaded successfully!")
            print("\n📋 Next Steps:")
            print("1. Test audio playback in the NIRA Tamil app")
            print("2. Verify voice selection functionality")
            print("3. Check audio quality and pronunciation")

async def main():
    """Main execution function"""
    try:
        uploader = LiteratureAudioUploader()
        await uploader.upload_all_audio()
        
    except KeyboardInterrupt:
        print("\n⏹️  Upload stopped by user")
    except Exception as e:
        print(f"\n❌ Fatal error: {e}")
        raise

if __name__ == "__main__":
    print("🚀 NIRA Tamil Literature Audio Uploader")
    print("☁️  Uploading audio files to Supabase storage")
    print("🗄️  Updating database with audio URLs")
    print()
    
    # Run the async main function
    asyncio.run(main())
