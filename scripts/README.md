# NIRA-Tamil Scripts Directory

This directory contains utility scripts for the NIRA-Tamil project development and maintenance.

## 📁 Directory Structure

### **Database Scripts**
- `create_conversation_lines_table.sql` - Database schema for conversation lines
- `create_explore_content_schema.sql` - Schema for explore content
- `create_tamil_writing_database_schema.sql` - Tamil writing feature schema
- `populate_comprehensive_content.sql` - Populate comprehensive content
- `populate_explore_content_data.sql` - Populate explore content data
- `populate_tamil_script_data.sql` - Tamil script data population
- `populate_tamil_script_tn_curriculum.sql` - TN curriculum data

### **Audio Generation Scripts**
- `generate_audio_only.py` - Generate audio files only
- `generate_grammar_audio.py` - Generate grammar lesson audio
- `generate_literature_audio.py` - Generate literature audio
- `generate_reading_audio.py` - Generate reading content audio
- `generate_practice_exercise_audio.py` - Generate practice exercise audio
- `quick_grammar_audio.py` - Quick grammar audio generation
- `run_audio_generation.sh` - Batch audio generation script
- `update_audio_urls.py` - Update audio URLs in database

### **Content Management Scripts**
- `batch_upload_a1_lessons.py` - Upload A1 level lessons
- `complete_culture_data.py` - Complete culture data processing
- `comprehensive_culture_content.py` - Comprehensive culture content
- `generate_culture_content.py` - Generate culture content
- `upload_a1_lesson.py` - Upload individual A1 lessons
- `upload_culture_content.py` - Upload culture content
- `upload_literature_audio.py` - Upload literature audio files

### **Setup and Configuration Scripts**
- `add_supabase_package.sh` - Add Supabase package to project
- `implement_supabase_integration.sh` - Implement Supabase integration
- `setup_supabase_package.sh` - Setup Supabase package

### **Testing Scripts**
- `test_audio_generation.py` - Test audio generation functionality
- `test_panchang_api.py` - Test Panchang API integration
- `test_reliable_panchang.py` - Test reliable Panchang service

### **Utility Scripts**
- `create_glassmorphic_nira_icon.py` - Create app icons with glassmorphic design
- `create_app_icon.py` - Create app icon assets
- `fix_practice_exercises.py` - Fix practice exercise data
- `requirements.txt` - Python dependencies

## 🚀 Usage

### **Audio Generation**
```bash
# Generate all audio files
./run_audio_generation.sh

# Generate specific audio type
python3 generate_grammar_audio.py
python3 generate_literature_audio.py
```

### **Database Setup**
```bash
# Setup Supabase integration
./setup_supabase_package.sh

# Run database migrations
psql -f create_explore_content_schema.sql
psql -f populate_comprehensive_content.sql
```

### **Content Upload**
```bash
# Upload A1 lessons
python3 batch_upload_a1_lessons.py

# Upload culture content
python3 upload_culture_content.py
```

### **Testing**
```bash
# Test audio generation
python3 test_audio_generation.py

# Test API integration
python3 test_panchang_api.py
```

## 📋 Requirements

- Python 3.8+
- PostgreSQL (for database scripts)
- Supabase CLI
- Google Cloud TTS API access
- Required Python packages (see requirements.txt)

## 🔧 Setup

1. Install Python dependencies:
```bash
pip install -r requirements.txt
```

2. Configure API keys in the main project config
3. Setup Supabase connection
4. Run setup scripts as needed

## ⚠️ Important Notes

- Always backup database before running migration scripts
- Test audio generation with small batches first
- Ensure API keys are properly configured
- Check file permissions for shell scripts

## 📚 Documentation

For detailed documentation on each script, refer to the main project docs directory.
