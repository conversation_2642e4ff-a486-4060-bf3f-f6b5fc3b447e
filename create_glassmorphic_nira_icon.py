#!/usr/bin/env python3
"""
NIRA Glassmorphic App Icon Generator
Creates a beautiful glassmorphic "N" logo similar to Google's "G" style
Modern, universal design without country-specific colors
"""

from PIL import Image, ImageDraw, ImageFont, ImageFilter
import math
import os

def create_glassmorphic_gradient(size, colors):
    """Create a smooth glassmorphic gradient background"""
    image = Image.new('RGBA', (size, size), (0, 0, 0, 0))
    draw = ImageDraw.Draw(image)
    
    # Create radial gradient effect
    center = size // 2
    max_radius = size // 2
    
    for radius in range(max_radius, 0, -1):
        # Calculate color interpolation
        ratio = radius / max_radius
        
        # Interpolate between colors
        if ratio > 0.7:
            # Outer edge - darker blue
            color = (45, 55, 72, int(255 * 0.9))  # Dark slate
        elif ratio > 0.4:
            # Middle - purple/blue
            color = (99, 102, 241, int(255 * 0.8))  # Indigo
        else:
            # Center - lighter blue/cyan
            color = (139, 92, 246, int(255 * 0.7))  # Purple
        
        draw.ellipse([
            center - radius, center - radius,
            center + radius, center + radius
        ], fill=color)
    
    return image

def create_glass_effect(image):
    """Add glassmorphic effects to the image"""
    # Create a copy for effects
    glass_image = image.copy()
    
    # Add subtle blur for glass effect
    blurred = glass_image.filter(ImageFilter.GaussianBlur(radius=1))
    
    # Create highlight overlay
    highlight = Image.new('RGBA', image.size, (255, 255, 255, 0))
    draw = ImageDraw.Draw(highlight)
    
    size = image.size[0]
    
    # Add top-left highlight for glass effect
    highlight_size = size // 3
    draw.ellipse([
        size // 6, size // 6,
        size // 6 + highlight_size, size // 6 + highlight_size
    ], fill=(255, 255, 255, 30))
    
    # Blend highlight with blurred image
    glass_image = Image.alpha_composite(blurred, highlight)
    
    return glass_image

def create_letter_n(size, color=(255, 255, 255, 255)):
    """Create a clean, modern letter 'N' similar to Google's style"""
    image = Image.new('RGBA', (size, size), (0, 0, 0, 0))
    draw = ImageDraw.Draw(image)
    
    # Calculate proportions
    margin = size * 0.25  # 25% margin on each side
    letter_width = size - (2 * margin)
    letter_height = letter_width
    
    # Stroke width proportional to size
    stroke_width = max(int(size * 0.08), 8)  # 8% of size, minimum 8px
    
    # Calculate N geometry
    left_x = margin
    right_x = margin + letter_width - stroke_width
    top_y = margin
    bottom_y = margin + letter_height
    
    # Draw left vertical line
    draw.rectangle([
        left_x, top_y,
        left_x + stroke_width, bottom_y
    ], fill=color)
    
    # Draw right vertical line
    draw.rectangle([
        right_x, top_y,
        right_x + stroke_width, bottom_y
    ], fill=color)
    
    # Draw diagonal line (more complex for clean appearance)
    # Create diagonal as a series of rectangles for smooth appearance
    diagonal_steps = int(letter_height)
    for i in range(diagonal_steps):
        progress = i / diagonal_steps
        
        # Calculate position along diagonal
        x = left_x + stroke_width + (progress * (letter_width - 2 * stroke_width))
        y = top_y + (progress * letter_height)
        
        # Draw small rectangle for this step
        step_size = stroke_width * 1.2
        draw.ellipse([
            x - step_size//2, y - step_size//2,
            x + step_size//2, y + step_size//2
        ], fill=color)
    
    return image

def create_nira_icon(size=1024):
    """Create the complete NIRA glassmorphic icon"""
    
    # Create base glassmorphic background
    background = create_glassmorphic_gradient(size, [
        (45, 55, 72),    # Dark slate
        (99, 102, 241),  # Indigo  
        (139, 92, 246)   # Purple
    ])
    
    # Apply glass effects
    glass_bg = create_glass_effect(background)
    
    # Create the letter N
    letter_n = create_letter_n(size, color=(255, 255, 255, 240))
    
    # Combine background and letter
    final_icon = Image.alpha_composite(glass_bg, letter_n)
    
    # Add subtle outer glow
    glow = Image.new('RGBA', (size + 20, size + 20), (0, 0, 0, 0))
    glow_draw = ImageDraw.Draw(glow)
    
    # Create glow effect
    for i in range(10, 0, -1):
        alpha = int(20 * (i / 10))
        glow_draw.ellipse([
            10 - i, 10 - i,
            size + 10 + i, size + 10 + i
        ], outline=(139, 92, 246, alpha), width=2)
    
    # Paste icon onto glow
    glow.paste(final_icon, (10, 10), final_icon)
    
    # Crop back to original size
    final_icon = glow.crop((10, 10, size + 10, size + 10))
    
    return final_icon

def create_ios_icon_set():
    """Create all iOS icon sizes"""
    
    # iOS icon sizes
    ios_sizes = [
        (1024, "<EMAIL>"),
        (180, "<EMAIL>"),
        (120, "<EMAIL>"),
        (167, "<EMAIL>"),
        (152, "<EMAIL>"),
        (76, "<EMAIL>"),
        (60, "<EMAIL>"),
        (40, "<EMAIL>"),
        (58, "<EMAIL>"),
        (87, "<EMAIL>"),
        (29, "<EMAIL>"),
        (20, "<EMAIL>")
    ]
    
    print("🎨 Creating NIRA Glassmorphic Icon Set...")
    print("=" * 50)
    
    # Create output directory
    output_dir = "NIRA-Tamil/Assets.xcassets/AppIcon.appiconset"
    os.makedirs(output_dir, exist_ok=True)
    
    # Generate master icon at highest resolution
    print("📐 Generating master 1024x1024 icon...")
    master_icon = create_nira_icon(1024)
    
    # Save all sizes
    for size, filename in ios_sizes:
        print(f"📱 Creating {size}x{size} - {filename}")
        
        if size == 1024:
            # Use master icon
            icon = master_icon
        else:
            # Resize master icon with high quality
            icon = master_icon.resize((size, size), Image.Resampling.LANCZOS)
        
        # Save with optimization
        filepath = os.path.join(output_dir, filename)
        icon.save(filepath, "PNG", optimize=True)
        print(f"   ✅ Saved: {filepath}")
    
    # Create additional marketing assets
    print("\n🎯 Creating Marketing Assets...")
    
    # App Store preview (1024x1024)
    marketing_dir = "NIRA-Tamil/Marketing-Assets"
    os.makedirs(marketing_dir, exist_ok=True)
    
    master_icon.save(f"{marketing_dir}/NIRA-App-Icon-1024.png", "PNG", optimize=True)
    print(f"   ✅ App Store Icon: {marketing_dir}/NIRA-App-Icon-1024.png")
    
    # Web favicon sizes
    favicon_sizes = [512, 256, 128, 64, 32, 16]
    for size in favicon_sizes:
        favicon = master_icon.resize((size, size), Image.Resampling.LANCZOS)
        favicon.save(f"{marketing_dir}/NIRA-Favicon-{size}.png", "PNG", optimize=True)
        print(f"   ✅ Favicon {size}x{size}: {marketing_dir}/NIRA-Favicon-{size}.png")
    
    # Social media assets
    print("\n📱 Creating Social Media Assets...")
    
    # Instagram/Twitter profile (400x400)
    social_icon = master_icon.resize((400, 400), Image.Resampling.LANCZOS)
    social_icon.save(f"{marketing_dir}/NIRA-Social-Profile-400.png", "PNG", optimize=True)
    print(f"   ✅ Social Profile: {marketing_dir}/NIRA-Social-Profile-400.png")
    
    return master_icon

def create_design_variations():
    """Create different design variations for comparison"""
    print("\n🎨 Creating Design Variations...")
    
    variations_dir = "NIRA-Tamil/Icon-Variations"
    os.makedirs(variations_dir, exist_ok=True)
    
    # Variation 1: Current glassmorphic design
    icon1 = create_nira_icon(512)
    icon1.save(f"{variations_dir}/NIRA-Glassmorphic-Purple.png", "PNG")
    print("   ✅ Variation 1: Glassmorphic Purple")
    
    # Variation 2: Blue/Cyan theme
    def create_blue_variant():
        background = create_glassmorphic_gradient(512, [
            (30, 58, 138),   # Blue-800
            (59, 130, 246),  # Blue-500
            (14, 165, 233)   # Sky-500
        ])
        glass_bg = create_glass_effect(background)
        letter_n = create_letter_n(512, color=(255, 255, 255, 240))
        return Image.alpha_composite(glass_bg, letter_n)
    
    icon2 = create_blue_variant()
    icon2.save(f"{variations_dir}/NIRA-Glassmorphic-Blue.png", "PNG")
    print("   ✅ Variation 2: Glassmorphic Blue")
    
    # Variation 3: Teal/Green theme
    def create_teal_variant():
        background = create_glassmorphic_gradient(512, [
            (13, 148, 136),  # Teal-600
            (20, 184, 166),  # Teal-500
            (45, 212, 191)   # Teal-400
        ])
        glass_bg = create_glass_effect(background)
        letter_n = create_letter_n(512, color=(255, 255, 255, 240))
        return Image.alpha_composite(glass_bg, letter_n)
    
    icon3 = create_teal_variant()
    icon3.save(f"{variations_dir}/NIRA-Glassmorphic-Teal.png", "PNG")
    print("   ✅ Variation 3: Glassmorphic Teal")

if __name__ == "__main__":
    print("🌟 NIRA Glassmorphic Icon Generator")
    print("Creating modern 'N' logo with glassmorphic design")
    print("=" * 60)
    
    try:
        # Create the complete icon set
        master_icon = create_ios_icon_set()
        
        # Create design variations
        create_design_variations()
        
        print("\n" + "=" * 60)
        print("🎉 SUCCESS! NIRA Glassmorphic Icons Created!")
        print("=" * 60)
        print("📱 iOS App Icons: NIRA-Tamil/Assets.xcassets/AppIcon.appiconset/")
        print("🎯 Marketing Assets: NIRA-Tamil/Marketing-Assets/")
        print("🎨 Design Variations: NIRA-Tamil/Icon-Variations/")
        print("\n✨ Features:")
        print("   • Modern glassmorphic design")
        print("   • Clean 'N' letter like Google's 'G'")
        print("   • Universal colors (no country flags)")
        print("   • Professional gradient backgrounds")
        print("   • Glass effects with highlights")
        print("   • All iOS sizes generated")
        print("   • Marketing and social media assets")
        print("   • Multiple color variations")
        print("\n🚀 Ready to replace the old icon in Xcode!")
        
    except Exception as e:
        print(f"❌ Error: {e}")
        print("Make sure you have Pillow installed: pip install Pillow")
