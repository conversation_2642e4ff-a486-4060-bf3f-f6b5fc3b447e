#!/bin/bash

# NIRA Waiting List Website - S3 Deployment Script
# Deploy to learnnira.app S3 bucket with CloudFront invalidation

set -e  # Exit on any error

# Configuration
BUCKET_NAME="learnnira.app"
CLOUDFRONT_DISTRIBUTION_ID=""  # Add your CloudFront distribution ID here
REGION="us-west-1"  # Change to your preferred region

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}🚀 NIRA Waiting List Website Deployment${NC}"
echo -e "${BLUE}======================================${NC}"

# Check if AWS CLI is installed
if ! command -v aws &> /dev/null; then
    echo -e "${RED}❌ AWS CLI is not installed. Please install it first.${NC}"
    echo "Install: https://docs.aws.amazon.com/cli/latest/userguide/getting-started-install.html"
    exit 1
fi

# Check if AWS credentials are configured
if ! aws sts get-caller-identity &> /dev/null; then
    echo -e "${RED}❌ AWS credentials not configured. Please run 'aws configure' first.${NC}"
    exit 1
fi

echo -e "${GREEN}✅ AWS CLI configured and ready${NC}"

# Create S3 bucket if it doesn't exist
echo -e "${YELLOW}📦 Checking S3 bucket: ${BUCKET_NAME}${NC}"

if ! aws s3 ls "s3://${BUCKET_NAME}" 2>&1 | grep -q 'NoSuchBucket'; then
    echo -e "${GREEN}✅ Bucket ${BUCKET_NAME} exists${NC}"
else
    echo -e "${YELLOW}📦 Creating S3 bucket: ${BUCKET_NAME}${NC}"
    aws s3 mb "s3://${BUCKET_NAME}" --region "${REGION}"
    
    # Configure bucket for static website hosting
    echo -e "${YELLOW}🌐 Configuring static website hosting${NC}"
    aws s3 website "s3://${BUCKET_NAME}" \
        --index-document index.html \
        --error-document index.html
    
    # Set bucket policy for public read access
    echo -e "${YELLOW}🔓 Setting bucket policy for public access${NC}"
    cat > bucket-policy.json << EOF
{
    "Version": "2012-10-17",
    "Statement": [
        {
            "Sid": "PublicReadGetObject",
            "Effect": "Allow",
            "Principal": "*",
            "Action": "s3:GetObject",
            "Resource": "arn:aws:s3:::${BUCKET_NAME}/*"
        }
    ]
}
EOF
    
    aws s3api put-bucket-policy \
        --bucket "${BUCKET_NAME}" \
        --policy file://bucket-policy.json
    
    rm bucket-policy.json
    echo -e "${GREEN}✅ Bucket created and configured${NC}"
fi

# Validate required files exist
echo -e "${YELLOW}📋 Validating required files${NC}"

required_files=("index.html" "styles/main.css" "scripts/main.js")
missing_files=()

for file in "${required_files[@]}"; do
    if [[ ! -f "$file" ]]; then
        missing_files+=("$file")
    fi
done

if [[ ${#missing_files[@]} -gt 0 ]]; then
    echo -e "${RED}❌ Missing required files:${NC}"
    for file in "${missing_files[@]}"; do
        echo -e "${RED}   - $file${NC}"
    done
    exit 1
fi

echo -e "${GREEN}✅ All required files present${NC}"

# Check for assets directory
if [[ ! -d "assets" ]]; then
    echo -e "${YELLOW}⚠️  Assets directory not found. Creating placeholder...${NC}"
    mkdir -p assets
    echo "Add your logo, app previews, and favicon files to this directory" > assets/README.txt
fi

# Sync files to S3
echo -e "${YELLOW}📤 Uploading files to S3...${NC}"

# Upload HTML files with proper content type and cache settings
aws s3 sync . "s3://${BUCKET_NAME}" \
    --exclude "*.sh" \
    --exclude "*.md" \
    --exclude ".git/*" \
    --exclude "*.DS_Store" \
    --exclude "node_modules/*" \
    --delete \
    --cache-control "public, max-age=31536000" \
    --metadata-directive REPLACE

# Set specific cache headers for HTML (shorter cache)
aws s3 cp index.html "s3://${BUCKET_NAME}/index.html" \
    --content-type "text/html" \
    --cache-control "public, max-age=3600" \
    --metadata-directive REPLACE

# Set specific cache headers for CSS and JS (longer cache with versioning)
aws s3 sync styles/ "s3://${BUCKET_NAME}/styles/" \
    --content-type "text/css" \
    --cache-control "public, max-age=31536000" \
    --metadata-directive REPLACE

aws s3 sync scripts/ "s3://${BUCKET_NAME}/scripts/" \
    --content-type "application/javascript" \
    --cache-control "public, max-age=31536000" \
    --metadata-directive REPLACE

echo -e "${GREEN}✅ Files uploaded successfully${NC}"

# Get S3 website URL
WEBSITE_URL="http://${BUCKET_NAME}.s3-website-${REGION}.amazonaws.com"
echo -e "${GREEN}🌐 S3 Website URL: ${WEBSITE_URL}${NC}"

# CloudFront invalidation (if distribution ID is provided)
if [[ -n "$CLOUDFRONT_DISTRIBUTION_ID" ]]; then
    echo -e "${YELLOW}🔄 Creating CloudFront invalidation...${NC}"
    
    INVALIDATION_ID=$(aws cloudfront create-invalidation \
        --distribution-id "$CLOUDFRONT_DISTRIBUTION_ID" \
        --paths "/*" \
        --query 'Invalidation.Id' \
        --output text)
    
    echo -e "${GREEN}✅ CloudFront invalidation created: ${INVALIDATION_ID}${NC}"
    echo -e "${YELLOW}⏳ Invalidation may take 5-15 minutes to complete${NC}"
else
    echo -e "${YELLOW}⚠️  CloudFront distribution ID not set. Skipping invalidation.${NC}"
    echo -e "${YELLOW}   Add CLOUDFRONT_DISTRIBUTION_ID to this script for automatic invalidation.${NC}"
fi

# Display deployment summary
echo -e "${BLUE}📊 Deployment Summary${NC}"
echo -e "${BLUE}===================${NC}"
echo -e "${GREEN}✅ Bucket: ${BUCKET_NAME}${NC}"
echo -e "${GREEN}✅ Region: ${REGION}${NC}"
echo -e "${GREEN}✅ Files: Uploaded and synced${NC}"
echo -e "${GREEN}✅ Cache: Optimized headers set${NC}"

if [[ -n "$CLOUDFRONT_DISTRIBUTION_ID" ]]; then
    echo -e "${GREEN}✅ CDN: CloudFront invalidation triggered${NC}"
    echo -e "${GREEN}🌐 Live URL: https://learnnira.app${NC}"
else
    echo -e "${YELLOW}⚠️  CDN: Manual CloudFront setup required${NC}"
    echo -e "${GREEN}🌐 S3 URL: ${WEBSITE_URL}${NC}"
fi

echo ""
echo -e "${BLUE}🎉 Deployment Complete!${NC}"
echo -e "${GREEN}Your NIRA waiting list website is now live and ready to collect signups!${NC}"

# Optional: Open website in browser (macOS)
if [[ "$OSTYPE" == "darwin"* ]]; then
    read -p "Open website in browser? (y/n): " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        if [[ -n "$CLOUDFRONT_DISTRIBUTION_ID" ]]; then
            open "https://learnnira.app"
        else
            open "$WEBSITE_URL"
        fi
    fi
fi

echo -e "${BLUE}📧 Next Steps:${NC}"
echo -e "${YELLOW}1. Add your logo and app preview images to the assets/ folder${NC}"
echo -e "${YELLOW}2. Test the waitlist signup form${NC}"
echo -e "${YELLOW}3. Set up CloudFront distribution for HTTPS and better performance${NC}"
echo -e "${YELLOW}4. Configure custom domain DNS (learnnira.app)${NC}"
echo -e "${YELLOW}5. Set up monitoring and analytics${NC}"

echo ""
echo -e "${GREEN}🚀 Ready to collect your first heritage language learners!${NC}"
