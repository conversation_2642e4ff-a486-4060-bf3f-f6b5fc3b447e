// NIRA Waiting List Website Configuration
// Customize these settings for your deployment

window.NIRA_CONFIG = {
  // Supabase Configuration
  supabase: {
    url: 'https://wnsorhbsucjguaoquhvr.supabase.co',
    anonKey: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Induc29yaGJzdWNqZ3Vhb3F1aHZyIiwicm9sZSI6ImFub24iLCJpYXQiOjE3MTg3MzY5OTgsImV4cCI6MjAzNDMxMjk5OH0.VGJzOGJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9',
    tableName: 'waitlist_signups'
  },

  // Website Information
  site: {
    name: 'NIRA',
    fullName: 'NIRA - Learn Your Heritage Language',
    domain: 'learnnira.app',
    email: '<EMAIL>',
    description: 'AI-powered heritage language learning that honors tradition while embracing innovation.',
    tagline: 'Preserving heritage languages through AI-powered cultural authenticity'
  },

  // Social Media Links
  social: {
    twitter: 'https://twitter.com/learnnira',
    facebook: 'https://facebook.com/learnnira',
    instagram: 'https://instagram.com/learnnira',
    linkedin: 'https://linkedin.com/company/learnnira',
    youtube: 'https://youtube.com/@learnnira'
  },

  // Analytics Configuration
  analytics: {
    googleAnalytics: '', // Add your GA4 measurement ID
    facebookPixel: '',   // Add your Facebook Pixel ID
    hotjar: '',          // Add your Hotjar site ID
    mixpanel: ''         // Add your Mixpanel project token
  },

  // Email Marketing Integration
  email: {
    provider: 'supabase', // 'supabase', 'mailchimp', 'convertkit', 'sendgrid'
    mailchimp: {
      apiKey: '',
      listId: ''
    },
    convertkit: {
      apiKey: '',
      formId: ''
    }
  },

  // Feature Flags
  features: {
    enableAnalytics: true,
    enableSocialSharing: true,
    enableBlogSection: false,
    enableTeamSection: false,
    enablePricingSection: false,
    enableFAQSection: false,
    enableNewsletterPopup: false,
    enableChatWidget: false
  },

  // Content Configuration
  content: {
    // Hero Section
    hero: {
      title: 'Learn Your <span class="gradient-text">Heritage Language</span><br>with AI-Powered Cultural Authenticity',
      subtitle: 'NIRA bridges tradition and innovation, helping you connect with your cultural roots through personalized, AI-driven language learning that honors your heritage.',
      ctaText: 'Join the Waitlist',
      ctaNote: 'Be among the first to experience heritage language learning reimagined'
    },

    // Languages Available
    languages: [
      {
        code: 'tamil',
        name: 'Tamil',
        script: 'தமிழ்',
        status: 'Available First',
        description: 'One of the world\'s oldest languages, spoken by 75+ million people worldwide. Rich literary tradition spanning over 2,000 years.',
        featured: true
      },
      {
        code: 'hindi',
        name: 'Hindi',
        script: 'हिंदी',
        status: 'Coming Soon',
        featured: false
      },
      {
        code: 'bengali',
        name: 'Bengali',
        script: 'বাংলা',
        status: 'Coming Soon',
        featured: false
      },
      {
        code: 'telugu',
        name: 'Telugu',
        script: 'తెలుగు',
        status: 'Coming Soon',
        featured: false
      },
      {
        code: 'gujarati',
        name: 'Gujarati',
        script: 'ગુજરાતી',
        status: 'Coming Soon',
        featured: false
      },
      {
        code: 'punjabi',
        name: 'Punjabi',
        script: 'ਪੰਜਾਬੀ',
        status: 'Coming Soon',
        featured: false
      }
    ],

    // Testimonials
    testimonials: [
      {
        text: "I've tried many language apps, but none understood the cultural context like NIRA promises to. As a second-generation Tamil-American, I need more than just vocabulary - I need to connect with my heritage.",
        author: {
          name: 'Priya Krishnan',
          title: 'Software Engineer, San Francisco',
          avatar: 'P'
        }
      },
      {
        text: "My children are losing touch with our Tamil roots. Existing apps teach language but miss the cultural soul. NIRA's approach to heritage learning is exactly what our community needs.",
        author: {
          name: 'Rajesh Patel',
          title: 'Parent & Community Leader, Toronto',
          avatar: 'R'
        }
      },
      {
        text: "As a Tamil teacher, I see students struggling with apps that don't respect our language's depth. NIRA's AI-powered cultural authenticity could revolutionize how we preserve our heritage.",
        author: {
          name: 'Meera Sundaram',
          title: 'Tamil Language Educator, London',
          avatar: 'M'
        }
      }
    ],

    // Features
    features: [
      {
        icon: '🎯',
        title: 'Cultural Authenticity',
        description: 'Learn with content created by native speakers and cultural experts, ensuring authentic pronunciation, context, and cultural nuances.'
      },
      {
        icon: '🤖',
        title: 'AI-Powered Personalization',
        description: 'Advanced AI adapts to your learning style, pace, and cultural background, creating a personalized journey that respects your heritage.'
      },
      {
        icon: '🌍',
        title: 'Community Connection',
        description: 'Connect with other heritage learners, share cultural stories, and build lasting relationships within your linguistic community.'
      },
      {
        icon: '📱',
        title: 'Modern Learning Experience',
        description: 'Beautiful, intuitive design meets cutting-edge technology to make heritage language learning enjoyable and effective.'
      }
    ],

    // Waitlist Benefits
    waitlistBenefits: [
      {
        icon: '🎯',
        text: 'Early access to Tamil learning'
      },
      {
        icon: '💎',
        text: 'Exclusive cultural content'
      },
      {
        icon: '🎁',
        text: 'Special launch pricing'
      }
    ]
  },

  // Form Configuration
  form: {
    requiredFields: ['email', 'name', 'language'],
    optionalFields: ['background'],
    validationRules: {
      email: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
      name: /^[a-zA-Z\s]{2,50}$/
    },
    successRedirect: null, // URL to redirect after successful signup
    errorRetryAttempts: 3
  },

  // UI Configuration
  ui: {
    theme: 'glassmorphic',
    primaryColor: '#FF6B35',
    secondaryColor: '#4ECDC4',
    accentColor: '#FFE66D',
    animationDuration: 300,
    enableParticles: false,
    enableScrollAnimations: true,
    enableTypingEffect: false
  },

  // Performance Configuration
  performance: {
    lazyLoadImages: true,
    preloadCriticalAssets: true,
    enableServiceWorker: false,
    cacheStrategy: 'networkFirst'
  },

  // SEO Configuration
  seo: {
    title: 'NIRA - Learn Your Heritage Language | AI-Powered Cultural Learning',
    description: 'Join the waitlist for NIRA - the AI-powered heritage language learning app that honors tradition while embracing innovation. Starting with Tamil, expanding to all heritage languages.',
    keywords: 'heritage language learning, Tamil learning app, AI language learning, cultural authenticity, language preservation',
    ogImage: 'https://learnnira.app/assets/nira-social-preview.png',
    twitterCard: 'summary_large_image'
  }
};

// Export for use in other scripts
if (typeof module !== 'undefined' && module.exports) {
  module.exports = window.NIRA_CONFIG;
}
