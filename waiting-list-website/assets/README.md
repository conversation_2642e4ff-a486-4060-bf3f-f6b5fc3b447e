# NIRA Waiting List Website Assets

This folder contains all the visual assets for the NIRA waiting list website.

## 📁 Required Assets

### 🎨 Logo Files
- **`nira-logo-horizontal.png`** (400x120px) - Main horizontal logo for navigation
- **`nira-logo-compact.png`** (120x120px) - Square logo for mobile/compact spaces
- **`nira-logo-white.png`** (400x120px) - White version for dark backgrounds

### 📱 App Preview Images
- **`app-preview.png`** (300x600px) - Main app screenshot for hero section
- **`app-mockup-1.png`** (300x600px) - Additional app screenshots
- **`app-mockup-2.png`** (300x600px) - Feature demonstrations
- **`app-mockup-3.png`** (300x600px) - Cultural content examples

### 🌐 Favicon & Icons
- **`favicon.ico`** (32x32px) - Standard favicon
- **`favicon-16x16.png`** (16x16px) - Small favicon
- **`favicon-32x32.png`** (32x32px) - Standard favicon
- **`apple-touch-icon.png`** (180x180px) - Apple touch icon
- **`android-chrome-192x192.png`** (192x192px) - Android icon
- **`android-chrome-512x512.png`** (512x512px) - Android icon large

### 📊 Social Media Assets
- **`nira-social-preview.png`** (1200x630px) - Open Graph/Twitter card image
- **`nira-facebook-cover.png`** (1200x315px) - Facebook cover image
- **`nira-linkedin-banner.png`** (1584x396px) - LinkedIn banner
- **`nira-instagram-post.png`** (1080x1080px) - Instagram square post

## 🎨 Asset Creation Guidelines

### Logo Usage
- Use horizontal logo in navigation and headers
- Use compact logo in mobile navigation and small spaces
- Maintain minimum clear space of 20px around logos
- Never stretch or distort logo proportions

### App Screenshots
- Use actual app screenshots when available
- Show key features: Tamil learning, cultural content, AI assistance
- Maintain consistent device mockup style
- Optimize for web (compress without quality loss)

### Color Consistency
Ensure all assets use the NIRA brand colors:
- **Primary Orange**: #FF6B35
- **Secondary Teal**: #4ECDC4
- **Accent Gold**: #FFE66D
- **Text Dark**: #2C3E50

### File Formats
- **Logos**: PNG with transparent background
- **Screenshots**: PNG for quality, JPG for smaller file sizes
- **Icons**: PNG format
- **Social**: JPG optimized for web

## 🔧 Asset Optimization

### Recommended Tools
- **TinyPNG**: Compress PNG files
- **ImageOptim**: Mac app for image optimization
- **Squoosh**: Google's web-based image optimizer
- **Figma**: Design and export assets

### Optimization Settings
- **Quality**: 85-90% for JPG files
- **Compression**: Lossless for PNG logos
- **Format**: WebP for modern browsers (optional)
- **Responsive**: Create @2x versions for retina displays

## 📱 Responsive Considerations

### Logo Sizes
```css
/* Desktop */
.nav-logo { height: 40px; }

/* Tablet */
@media (max-width: 768px) {
  .nav-logo { height: 36px; }
}

/* Mobile */
@media (max-width: 480px) {
  .nav-logo { height: 32px; }
}
```

### App Preview Sizes
- **Desktop**: 300x600px (shown in hero)
- **Tablet**: 250x500px (responsive scaling)
- **Mobile**: 200x400px (smaller display)

## 🎯 Asset Checklist

Before deploying, ensure you have:

### Essential Assets (Required)
- [ ] `nira-logo-horizontal.png` - Navigation logo
- [ ] `app-preview.png` - Hero section app screenshot
- [ ] `favicon-32x32.png` - Browser favicon
- [ ] `apple-touch-icon.png` - iOS home screen icon
- [ ] `nira-social-preview.png` - Social media sharing

### Enhanced Assets (Recommended)
- [ ] `nira-logo-white.png` - Dark background version
- [ ] `app-mockup-1.png` - Additional screenshots
- [ ] `app-mockup-2.png` - Feature demonstrations
- [ ] `favicon-16x16.png` - Small favicon
- [ ] `android-chrome-192x192.png` - Android icon

### Marketing Assets (Optional)
- [ ] `nira-facebook-cover.png` - Facebook page cover
- [ ] `nira-linkedin-banner.png` - LinkedIn company banner
- [ ] `nira-instagram-post.png` - Instagram promotional posts
- [ ] Team photos for about section
- [ ] Cultural imagery for backgrounds

## 🚀 Quick Asset Generation

### From Existing NIRA App Icons
You can extract assets from the iOS app icons we created:

```bash
# Copy from iOS app assets
cp ../NIRA-Tamil/Assets.xcassets/AppIcon.appiconset/<EMAIL> ./nira-app-icon-1024.png

# Resize for web use (requires ImageMagick)
convert nira-app-icon-1024.png -resize 400x120 nira-logo-horizontal.png
convert nira-app-icon-1024.png -resize 180x180 apple-touch-icon.png
convert nira-app-icon-1024.png -resize 32x32 favicon-32x32.png
```

### Placeholder Creation
If you don't have assets yet, the website will work with placeholders:
- Logo areas will show "NIRA" text
- App preview will show a gradient placeholder
- Icons will use browser defaults

## 📞 Asset Support

Need help creating assets?
- **Email**: <EMAIL>
- **Design Guidelines**: See NIRA_BRANDING_GUIDE.md
- **Brand Colors**: See main.css color variables

---

**Ready to make NIRA's visual identity shine! ✨**
