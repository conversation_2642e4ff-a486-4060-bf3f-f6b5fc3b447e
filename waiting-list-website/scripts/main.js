// NIRA Waiting List Website - Main JavaScript
// Supabase Integration for Waitlist Collection

// Supabase Configuration
const SUPABASE_URL = 'https://wnsorhbsucjguaoquhvr.supabase.co';
const SUPABASE_ANON_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Induc29yaGJzdWNqZ3Vhb3F1aHZyIiwicm9sZSI6ImFub24iLCJpYXQiOjE3MTg3MzY5OTgsImV4cCI6MjAzNDMxMjk5OH0.VGJzOGJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9';

// Initialize Supabase client
const supabase = window.supabase.createClient(SUPABASE_URL, SUPABASE_ANON_KEY);

// DOM Elements
const waitlistForm = document.getElementById('waitlistForm');
const submitBtn = document.getElementById('submitBtn');
const submitText = submitBtn.querySelector('.submit-text');
const submitLoading = submitBtn.querySelector('.submit-loading');
const successMessage = document.getElementById('successMessage');
const formContainer = document.querySelector('.waitlist-form-container');

// Form submission handler
waitlistForm.addEventListener('submit', async (e) => {
    e.preventDefault();

    // Get form data
    const formData = new FormData(waitlistForm);
    const waitlistData = {
        email: formData.get('email').trim().toLowerCase(),
        name: formData.get('name').trim(),
        language: formData.get('language'),
        background: formData.get('background') || null,
        created_at: new Date().toISOString(),
        source: 'website_waitlist',
        status: 'active'
    };

    // Validate required fields
    if (!waitlistData.email || !waitlistData.name || !waitlistData.language) {
        showError('Please fill in all required fields.');
        return;
    }

    // Validate email format
    if (!isValidEmail(waitlistData.email)) {
        showError('Please enter a valid email address.');
        return;
    }

    try {
        // Show loading state
        setLoadingState(true);

        // Check if email already exists
        const { data: existingUser, error: checkError } = await supabase
            .from('waitlist_signups')
            .select('email')
            .eq('email', waitlistData.email)
            .single();

        if (existingUser) {
            showError('This email is already on our waitlist! We\'ll notify you when NIRA is ready.');
            setLoadingState(false);
            return;
        }

        // Insert new waitlist signup
        const { data, error } = await supabase
            .from('waitlist_signups')
            .insert([waitlistData])
            .select();

        if (error) {
            console.error('Supabase error:', error);
            throw error;
        }

        // Success! Show success message
        showSuccessMessage();

        // Track analytics (if you have analytics setup)
        trackWaitlistSignup(waitlistData);

    } catch (error) {
        console.error('Error submitting waitlist form:', error);
        showError('Something went wrong. Please try again or contact <NAME_EMAIL>');
    } finally {
        setLoadingState(false);
    }
});

// Helper Functions
function setLoadingState(loading) {
    submitBtn.disabled = loading;
    if (loading) {
        submitText.style.display = 'none';
        submitLoading.style.display = 'inline';
    } else {
        submitText.style.display = 'inline';
        submitLoading.style.display = 'none';
    }
}

function showSuccessMessage() {
    waitlistForm.style.display = 'none';
    successMessage.style.display = 'block';

    // Add celebration animation
    successMessage.style.animation = 'fadeInUp 0.6s ease-out';

    // Scroll to success message
    successMessage.scrollIntoView({ behavior: 'smooth', block: 'center' });
}

function showError(message) {
    // Remove any existing error messages
    const existingError = document.querySelector('.error-message');
    if (existingError) {
        existingError.remove();
    }

    // Create error message element
    const errorDiv = document.createElement('div');
    errorDiv.className = 'error-message';
    errorDiv.style.cssText = `
        background: rgba(231, 76, 60, 0.1);
        border: 1px solid rgba(231, 76, 60, 0.3);
        color: #E74C3C;
        padding: 16px;
        border-radius: 12px;
        margin-bottom: 20px;
        font-size: 0.9rem;
        backdrop-filter: blur(10px);
    `;
    errorDiv.textContent = message;

    // Insert error message before the form
    waitlistForm.insertBefore(errorDiv, waitlistForm.firstChild);

    // Remove error message after 5 seconds
    setTimeout(() => {
        if (errorDiv.parentNode) {
            errorDiv.remove();
        }
    }, 5000);
}

function isValidEmail(email) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
}

function trackWaitlistSignup(data) {
    // Track with analytics if available
    if (typeof gtag !== 'undefined') {
        gtag('event', 'waitlist_signup', {
            'language': data.language,
            'background': data.background,
            'source': 'website'
        });
    }

    // Track with Facebook Pixel if available
    if (typeof fbq !== 'undefined') {
        fbq('track', 'Lead', {
            content_name: 'NIRA Waitlist Signup',
            content_category: 'Language Learning'
        });
    }
}

// Smooth scrolling for navigation links
function scrollToWaitlist() {
    document.getElementById('waitlist').scrollIntoView({
        behavior: 'smooth'
    });
}

// Navigation smooth scrolling
document.querySelectorAll('a[href^="#"]').forEach(anchor => {
    anchor.addEventListener('click', function (e) {
        e.preventDefault();
        const target = document.querySelector(this.getAttribute('href'));
        if (target) {
            target.scrollIntoView({
                behavior: 'smooth',
                block: 'start'
            });
        }
    });
});

// Intersection Observer for animations
const observerOptions = {
    threshold: 0.1,
    rootMargin: '0px 0px -50px 0px'
};

const observer = new IntersectionObserver((entries) => {
    entries.forEach(entry => {
        if (entry.isIntersecting) {
            entry.target.style.animation = 'fadeInUp 0.6s ease-out forwards';
        }
    });
}, observerOptions);

// Observe elements for animation
document.addEventListener('DOMContentLoaded', () => {
    const animateElements = document.querySelectorAll('.feature-card, .language-card, .testimonial-card');
    animateElements.forEach(el => {
        el.style.opacity = '0';
        el.style.transform = 'translateY(30px)';
        observer.observe(el);
    });
});

// Add CSS animations
const style = document.createElement('style');
style.textContent = `
    @keyframes fadeInUp {
        from {
            opacity: 0;
            transform: translateY(30px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }

    @keyframes pulse {
        0%, 100% {
            transform: scale(1);
        }
        50% {
            transform: scale(1.05);
        }
    }

    .success-message {
        animation: fadeInUp 0.6s ease-out;
    }

    .cta-primary:hover {
        animation: pulse 0.3s ease-in-out;
    }
`;
document.head.appendChild(style);

// Mobile menu toggle (if needed)
function toggleMobileMenu() {
    const navLinks = document.querySelector('.nav-links');
    navLinks.classList.toggle('mobile-open');
}

// Add mobile menu styles
const mobileStyle = document.createElement('style');
mobileStyle.textContent = `
    @media (max-width: 768px) {
        .nav-links {
            position: fixed;
            top: 70px;
            left: 0;
            right: 0;
            background: rgba(0, 0, 0, 0.95);
            backdrop-filter: blur(20px);
            flex-direction: column;
            padding: 20px;
            transform: translateY(-100%);
            transition: transform 0.3s ease;
        }

        .nav-links.mobile-open {
            transform: translateY(0);
        }

        .mobile-menu-toggle {
            display: block;
            background: none;
            border: none;
            color: white;
            font-size: 1.5rem;
            cursor: pointer;
        }
    }

    @media (min-width: 769px) {
        .mobile-menu-toggle {
            display: none;
        }
    }
`;
document.head.appendChild(mobileStyle);

// Console welcome message
console.log(`
🌟 Welcome to NIRA - Heritage Language Learning Platform
🚀 Built with love for heritage language communities worldwide
📧 Questions? Contact <NAME_EMAIL>
`);

// Export functions for global access
window.scrollToWaitlist = scrollToWaitlist;
window.toggleMobileMenu = toggleMobileMenu;