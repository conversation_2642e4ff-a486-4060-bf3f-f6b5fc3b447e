# NIRA Waiting List Website

A beautiful, glassmorphic waiting list website for NIRA - the AI-powered heritage language learning platform.

## 🌟 Features

- **Glassmorphic Design**: Modern, beautiful UI with glassmorphism effects
- **Supabase Integration**: Real-time database for waitlist collection
- **Responsive Design**: Works perfectly on all devices
- **Cultural Authenticity**: Heritage language scripts and cultural elements
- **Performance Optimized**: Fast loading, smooth animations
- **SEO Ready**: Optimized for search engines and social sharing

## 🚀 Quick Setup

### 1. Domain Configuration
- Domain: `learnnira.app`
- Hosting: AWS S3 + CloudFront
- SSL: Automatic via CloudFront

### 2. Supabase Configuration
- Project: NIRA-Tamil (ID: wnsorhbsucjguaoquhvr)
- Table: `waitlist_signups` (automatically created)
- API: Public read/write access for waitlist submissions

### 3. Assets Required
You'll need to add these assets to the `assets/` folder:

#### Required Images:
- `nira-logo-horizontal.png` - Main horizontal logo for navigation
- `app-preview.png` - App screenshot for hero section
- `favicon-32x32.png` - 32x32 favicon
- `favicon-16x16.png` - 16x16 favicon  
- `apple-touch-icon.png` - 180x180 Apple touch icon
- `nira-social-preview.png` - 1200x630 social media preview image

#### Optional Images:
- `app-mockup-1.png` - Additional app screenshots
- `app-mockup-2.png` - Feature demonstrations
- `team-photo.jpg` - Team/founder photos for about section

## 📁 File Structure

```
waiting-list-website/
├── index.html              # Main landing page
├── styles/
│   └── main.css            # Glassmorphic styles + responsive design
├── scripts/
│   └── main.js             # Supabase integration + form handling
├── assets/
│   ├── images/             # Logo, app previews, icons
│   └── icons/              # Favicons and app icons
└── README.md               # This file
```

## 🎨 Design System

### Colors (NIRA Brand)
- **Primary**: #FF6B35 (Orange)
- **Secondary**: #4ECDC4 (Teal)  
- **Accent**: #FFE66D (Gold)
- **Success**: #2ECC71 (Green)
- **Error**: #E74C3C (Red)

### Typography
- **Font**: Inter (Google Fonts)
- **Weights**: 300, 400, 500, 600, 700

### Glassmorphic Elements
- **Background**: Gradient overlay with blur effects
- **Cards**: Semi-transparent with backdrop-filter
- **Borders**: Subtle white borders with opacity
- **Shadows**: Soft, layered shadows for depth

## 📊 Database Schema

### waitlist_signups Table
```sql
- id (UUID, Primary Key)
- email (VARCHAR, Unique, Required)
- name (VARCHAR, Required)  
- language (VARCHAR, Required) - tamil, hindi, bengali, etc.
- background (VARCHAR, Optional) - heritage_speaker, beginner, etc.
- source (VARCHAR) - website_waitlist, social_media, etc.
- status (VARCHAR) - active, notified, unsubscribed
- created_at (TIMESTAMP)
- updated_at (TIMESTAMP)
- metadata (JSONB) - Additional tracking data
```

## 🔧 Customization

### Adding New Languages
1. Update the language dropdown in `index.html`
2. Add new language cards in the languages section
3. Update the testimonials with relevant community feedback

### Analytics Integration
The JavaScript includes hooks for:
- Google Analytics (gtag)
- Facebook Pixel (fbq)
- Custom tracking events

### Email Marketing Integration
Easy to connect with:
- Mailchimp
- ConvertKit  
- SendGrid
- Custom email services

## 🚀 Deployment to S3

### 1. Build Assets
```bash
# Optimize images
# Minify CSS/JS (optional)
# Generate favicons from main logo
```

### 2. S3 Upload
```bash
aws s3 sync . s3://learnnira.app --delete
aws s3 website s3://learnnira.app --index-document index.html
```

### 3. CloudFront Configuration
- Origin: S3 bucket
- SSL Certificate: Auto-generated
- Caching: Optimize for static content
- Compression: Enable Gzip

### 4. DNS Configuration
- Point `learnnira.app` to CloudFront distribution
- Add `www.learnnira.app` redirect

## 📈 Performance Optimizations

### Already Implemented:
- ✅ Optimized CSS with minimal dependencies
- ✅ Efficient JavaScript with modern APIs
- ✅ Responsive images and lazy loading
- ✅ Minimal external dependencies
- ✅ Compressed assets and efficient caching

### Recommended Additions:
- Image optimization (WebP format)
- Service Worker for offline functionality
- Progressive Web App (PWA) features
- Advanced analytics and heat mapping

## 🔒 Security & Privacy

### Data Protection:
- Email validation and sanitization
- Rate limiting on form submissions
- GDPR-compliant data collection
- Secure Supabase API integration

### Privacy Features:
- Clear privacy policy
- Unsubscribe functionality
- Data retention policies
- Cookie consent (if needed)

## 📱 Mobile Optimization

### Responsive Breakpoints:
- Desktop: 1200px+
- Tablet: 768px - 1199px
- Mobile: 320px - 767px

### Mobile Features:
- Touch-optimized form inputs
- Swipe-friendly navigation
- Optimized font sizes
- Fast loading on mobile networks

## 🎯 Conversion Optimization

### Current Features:
- Clear value proposition
- Social proof through testimonials
- Benefit-focused messaging
- Minimal friction signup process
- Cultural connection and authenticity

### A/B Testing Opportunities:
- Hero headline variations
- CTA button text and colors
- Form field requirements
- Testimonial selection and placement

## 📞 Support & Contact

- **Email**: <EMAIL>
- **Website**: https://learnnira.app
- **Documentation**: This README
- **Issues**: Contact development team

---

**Built with ❤️ for heritage language communities worldwide**

*Ready to preserve and celebrate linguistic diversity through AI-powered learning*
