/* NIRA Waiting List Website - Glassmorphic Design */

/* CSS Variables - NIRA Brand Colors */
:root {
  /* NIRA Brand Colors */
  --nira-primary: #FF6B35;
  --nira-secondary: #4ECDC4;
  --nira-accent: #FFE66D;
  --nira-success: #2ECC71;
  --nira-warning: #F39C12;
  --nira-error: #E74C3C;
  
  /* Glassmorphic Colors */
  --glass-bg: rgba(255, 255, 255, 0.1);
  --glass-border: rgba(255, 255, 255, 0.2);
  --glass-shadow: rgba(0, 0, 0, 0.1);
  
  /* Text Colors */
  --text-primary: #2C3E50;
  --text-secondary: #7F8C8D;
  --text-light: #BDC3C7;
  --text-white: #FFFFFF;
  
  /* Background Gradients */
  --bg-gradient: linear-gradient(135deg, #4ECDC4 0%, #FF6B35 100%);
  --card-gradient: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.05) 100%);
  
  /* Spacing */
  --container-max-width: 1200px;
  --section-padding: 80px 0;
  --border-radius: 16px;
  --border-radius-lg: 24px;
}

/* Reset and Base Styles */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  line-height: 1.6;
  color: var(--text-primary);
  background: var(--bg-gradient);
  background-attachment: fixed;
  overflow-x: hidden;
}

/* Container */
.container {
  max-width: var(--container-max-width);
  margin: 0 auto;
  padding: 0 20px;
}

/* Navigation */
.nav {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 1000;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(20px);
  border-bottom: 1px solid var(--glass-border);
  padding: 15px 0;
}

.nav-container {
  max-width: var(--container-max-width);
  margin: 0 auto;
  padding: 0 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.nav-logo {
  height: 40px;
  width: auto;
}

.nav-links {
  display: flex;
  align-items: center;
  gap: 30px;
}

.nav-link {
  color: var(--text-white);
  text-decoration: none;
  font-weight: 500;
  transition: all 0.3s ease;
  padding: 8px 16px;
  border-radius: 8px;
}

.nav-link:hover {
  background: rgba(255, 255, 255, 0.1);
  transform: translateY(-2px);
}

.nav-link.cta-button {
  background: var(--nira-accent);
  color: var(--text-primary);
  font-weight: 600;
  padding: 12px 24px;
  border-radius: 25px;
}

.nav-link.cta-button:hover {
  background: var(--nira-accent);
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(255, 230, 109, 0.3);
}

/* Hero Section */
.hero {
  position: relative;
  min-height: 100vh;
  display: flex;
  align-items: center;
  padding: 120px 0 80px;
  overflow: hidden;
}

.hero-background {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: var(--bg-gradient);
  opacity: 0.9;
}

.hero-content {
  position: relative;
  z-index: 2;
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 60px;
  align-items: center;
}

.hero-title {
  font-size: 3.5rem;
  font-weight: 700;
  line-height: 1.2;
  color: var(--text-white);
  margin-bottom: 24px;
}

.gradient-text {
  background: linear-gradient(135deg, var(--nira-accent) 0%, #FFF 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.hero-subtitle {
  font-size: 1.25rem;
  color: rgba(255, 255, 255, 0.9);
  margin-bottom: 40px;
  line-height: 1.6;
}

.hero-stats {
  display: flex;
  gap: 40px;
  margin-bottom: 40px;
}

.stat-item {
  text-align: center;
}

.stat-number {
  font-size: 2rem;
  font-weight: 700;
  color: var(--nira-accent);
  margin-bottom: 8px;
}

.stat-label {
  font-size: 0.9rem;
  color: rgba(255, 255, 255, 0.8);
  font-weight: 500;
}

.hero-cta {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.cta-primary {
  background: var(--nira-accent);
  color: var(--text-primary);
  border: none;
  padding: 18px 36px;
  border-radius: 50px;
  font-size: 1.1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 12px;
  width: fit-content;
}

.cta-primary:hover {
  transform: translateY(-3px);
  box-shadow: 0 12px 30px rgba(255, 230, 109, 0.4);
}

.cta-arrow {
  font-size: 1.2rem;
  transition: transform 0.3s ease;
}

.cta-primary:hover .cta-arrow {
  transform: translateX(4px);
}

.cta-note {
  color: rgba(255, 255, 255, 0.7);
  font-size: 0.9rem;
}

/* Phone Mockup */
.hero-visual {
  display: flex;
  justify-content: center;
  align-items: center;
}

.phone-mockup {
  position: relative;
  width: 300px;
  height: 600px;
  background: linear-gradient(145deg, #2C3E50, #34495E);
  border-radius: 40px;
  padding: 20px;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
  transform: perspective(1000px) rotateY(-15deg) rotateX(5deg);
}

.phone-screen {
  width: 100%;
  height: 100%;
  background: #000;
  border-radius: 30px;
  overflow: hidden;
  position: relative;
}

.app-preview {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

/* Section Styles */
section {
  padding: var(--section-padding);
  position: relative;
}

.section-header {
  text-align: center;
  margin-bottom: 60px;
}

.section-title {
  font-size: 2.5rem;
  font-weight: 700;
  color: var(--text-white);
  margin-bottom: 16px;
}

.section-subtitle {
  font-size: 1.1rem;
  color: rgba(255, 255, 255, 0.8);
  max-width: 600px;
  margin: 0 auto;
  line-height: 1.6;
}

/* Features Section */
.features {
  background: rgba(255, 255, 255, 0.05);
  backdrop-filter: blur(20px);
}

.features-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 30px;
}

.feature-card {
  background: var(--card-gradient);
  backdrop-filter: blur(20px);
  border: 1px solid var(--glass-border);
  border-radius: var(--border-radius);
  padding: 40px 30px;
  text-align: center;
  transition: all 0.3s ease;
}

.feature-card:hover {
  transform: translateY(-8px);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
  border-color: rgba(255, 230, 109, 0.3);
}

.feature-icon {
  font-size: 3rem;
  margin-bottom: 20px;
  display: block;
}

.feature-title {
  font-size: 1.3rem;
  font-weight: 600;
  color: var(--text-white);
  margin-bottom: 16px;
}

.feature-description {
  color: rgba(255, 255, 255, 0.8);
  line-height: 1.6;
}

/* Languages Section */
.languages-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 24px;
}

.language-card {
  background: var(--card-gradient);
  backdrop-filter: blur(20px);
  border: 1px solid var(--glass-border);
  border-radius: var(--border-radius);
  padding: 30px 24px;
  text-align: center;
  transition: all 0.3s ease;
}

.language-card.featured {
  border-color: var(--nira-accent);
  background: linear-gradient(135deg, rgba(255, 230, 109, 0.1) 0%, rgba(255, 230, 109, 0.05) 100%);
}

.language-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 15px 30px rgba(0, 0, 0, 0.1);
}

.language-script {
  font-size: 2.5rem;
  margin-bottom: 16px;
  color: var(--nira-accent);
  font-weight: 600;
}

.language-name {
  font-size: 1.2rem;
  font-weight: 600;
  color: var(--text-white);
  margin-bottom: 8px;
}

.language-status {
  color: var(--nira-accent);
  font-weight: 500;
  font-size: 0.9rem;
  margin-bottom: 12px;
}

.language-description {
  color: rgba(255, 255, 255, 0.7);
  font-size: 0.9rem;
  line-height: 1.5;
}

/* Testimonials Section */
.testimonials {
  background: rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(20px);
}

.testimonials-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 30px;
}

.testimonial-card {
  background: var(--card-gradient);
  backdrop-filter: blur(20px);
  border: 1px solid var(--glass-border);
  border-radius: var(--border-radius);
  padding: 30px;
  transition: all 0.3s ease;
}

.testimonial-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 15px 30px rgba(0, 0, 0, 0.1);
}

.testimonial-text {
  color: rgba(255, 255, 255, 0.9);
  font-style: italic;
  line-height: 1.6;
  margin-bottom: 24px;
  font-size: 1rem;
}

.testimonial-author {
  display: flex;
  align-items: center;
  gap: 16px;
}

.author-avatar {
  width: 50px;
  height: 50px;
  background: var(--nira-accent);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  color: var(--text-primary);
  font-size: 1.2rem;
}

.author-name {
  font-weight: 600;
  color: var(--text-white);
  margin-bottom: 4px;
}

.author-title {
  color: rgba(255, 255, 255, 0.7);
  font-size: 0.9rem;
}

/* Waitlist Section */
.waitlist {
  background: rgba(255, 255, 255, 0.05);
  backdrop-filter: blur(20px);
}

.waitlist-content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 60px;
  align-items: center;
}

.waitlist-title {
  font-size: 2.2rem;
  font-weight: 700;
  color: var(--text-white);
  margin-bottom: 20px;
  line-height: 1.3;
}

.waitlist-subtitle {
  color: rgba(255, 255, 255, 0.8);
  font-size: 1.1rem;
  line-height: 1.6;
  margin-bottom: 30px;
}

.waitlist-benefits {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.benefit-item {
  display: flex;
  align-items: center;
  gap: 12px;
  color: rgba(255, 255, 255, 0.9);
  font-weight: 500;
}

.benefit-icon {
  font-size: 1.2rem;
}

/* Form Styles */
.waitlist-form-container {
  background: var(--card-gradient);
  backdrop-filter: blur(20px);
  border: 1px solid var(--glass-border);
  border-radius: var(--border-radius-lg);
  padding: 40px;
}

.waitlist-form {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.form-label {
  color: var(--text-white);
  font-weight: 500;
  font-size: 0.9rem;
}

.form-input,
.form-select {
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid var(--glass-border);
  border-radius: 12px;
  padding: 16px;
  color: var(--text-white);
  font-size: 1rem;
  transition: all 0.3s ease;
}

.form-input:focus,
.form-select:focus {
  outline: none;
  border-color: var(--nira-accent);
  box-shadow: 0 0 0 3px rgba(255, 230, 109, 0.1);
  background: rgba(255, 255, 255, 0.15);
}

.form-input::placeholder {
  color: rgba(255, 255, 255, 0.5);
}

.form-select option {
  background: var(--text-primary);
  color: var(--text-white);
}

.form-submit {
  background: var(--nira-accent);
  color: var(--text-primary);
  border: none;
  border-radius: 12px;
  padding: 18px;
  font-size: 1.1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  margin-top: 8px;
}

.form-submit:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(255, 230, 109, 0.3);
}

.form-submit:disabled {
  opacity: 0.7;
  cursor: not-allowed;
}

.form-note {
  color: rgba(255, 255, 255, 0.6);
  font-size: 0.85rem;
  text-align: center;
  margin-top: -8px;
}

/* Success Message */
.success-message {
  text-align: center;
  padding: 40px;
}

.success-icon {
  font-size: 3rem;
  margin-bottom: 20px;
}

.success-title {
  font-size: 1.5rem;
  font-weight: 600;
  color: var(--text-white);
  margin-bottom: 12px;
}

.success-text {
  color: rgba(255, 255, 255, 0.8);
  line-height: 1.6;
}

/* Footer */
.footer {
  background: rgba(0, 0, 0, 0.2);
  backdrop-filter: blur(20px);
  border-top: 1px solid var(--glass-border);
  padding: 60px 0 30px;
}

.footer-content {
  display: grid;
  grid-template-columns: 1fr auto;
  gap: 60px;
  margin-bottom: 40px;
}

.footer-logo {
  height: 40px;
  width: auto;
  margin-bottom: 16px;
}

.footer-tagline {
  color: rgba(255, 255, 255, 0.7);
  max-width: 300px;
  line-height: 1.5;
}

.footer-links {
  display: flex;
  gap: 60px;
}

.footer-section {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.footer-title {
  color: var(--text-white);
  font-weight: 600;
  margin-bottom: 8px;
}

.footer-link {
  color: rgba(255, 255, 255, 0.7);
  text-decoration: none;
  transition: color 0.3s ease;
}

.footer-link:hover {
  color: var(--nira-accent);
}

.footer-bottom {
  border-top: 1px solid var(--glass-border);
  padding-top: 30px;
  text-align: center;
}

.footer-copyright {
  color: rgba(255, 255, 255, 0.6);
  font-size: 0.9rem;
}

/* Responsive Design */
@media (max-width: 768px) {
  .nav-links {
    display: none;
  }
  
  .hero-content {
    grid-template-columns: 1fr;
    text-align: center;
    gap: 40px;
  }
  
  .hero-title {
    font-size: 2.5rem;
  }
  
  .hero-stats {
    justify-content: center;
  }
  
  .waitlist-content {
    grid-template-columns: 1fr;
    gap: 40px;
  }
  
  .footer-content {
    grid-template-columns: 1fr;
    gap: 40px;
  }
  
  .footer-links {
    gap: 40px;
  }
  
  .testimonials-grid {
    grid-template-columns: 1fr;
  }
  
  .phone-mockup {
    transform: none;
    width: 250px;
    height: 500px;
  }
}

@media (max-width: 480px) {
  .container {
    padding: 0 16px;
  }
  
  .hero-title {
    font-size: 2rem;
  }
  
  .section-title {
    font-size: 2rem;
  }
  
  .waitlist-form-container {
    padding: 24px;
  }
  
  .hero-stats {
    flex-direction: column;
    gap: 20px;
  }
}
