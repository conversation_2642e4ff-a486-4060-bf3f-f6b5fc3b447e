<?xml version="1.0" encoding="UTF-8"?>
<svg width="200" height="200" viewBox="0 0 200 200" xmlns="http://www.w3.org/2000/svg">
  <!-- NIRA Primary Logo - Tamil Heritage AI Design -->
  
  <!-- Gradient Definitions -->
  <defs>
    <!-- Primary Radial Gradient -->
    <radialGradient id="niraGradient" cx="50%" cy="50%" r="50%">
      <stop offset="0%" style="stop-color:#4ECDC4;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#FF6B35;stop-opacity:1" />
    </radialGradient>
    
    <!-- AI Circuit Pattern -->
    <pattern id="circuitPattern" x="0" y="0" width="20" height="20" patternUnits="userSpaceOnUse">
      <rect width="20" height="20" fill="none"/>
      <circle cx="10" cy="10" r="1" fill="white" opacity="0.1"/>
      <line x1="5" y1="10" x2="15" y2="10" stroke="white" stroke-width="0.5" opacity="0.1"/>
      <line x1="10" y1="5" x2="10" y2="15" stroke="white" stroke-width="0.5" opacity="0.1"/>
    </pattern>
    
    <!-- Text Shadow Filter -->
    <filter id="textShadow" x="-50%" y="-50%" width="200%" height="200%">
      <feDropShadow dx="2" dy="2" stdDeviation="3" flood-color="#000000" flood-opacity="0.3"/>
    </filter>
  </defs>
  
  <!-- Main Background Circle -->
  <circle cx="100" cy="100" r="95" 
          fill="url(#niraGradient)" 
          stroke="#FFE66D" 
          stroke-width="3"/>
  
  <!-- AI Circuit Pattern Overlay -->
  <circle cx="100" cy="100" r="92" 
          fill="url(#circuitPattern)" 
          opacity="0.3"/>
  
  <!-- Tamil Script "நீ" (NIRA) -->
  <text x="100" y="125" 
        font-family="Tamil Sangam MN, Noto Sans Tamil, serif" 
        font-size="80" 
        font-weight="bold"
        fill="white" 
        text-anchor="middle" 
        filter="url(#textShadow)">நீ</text>
  
  <!-- Subtle Inner Glow -->
  <circle cx="100" cy="100" r="88" 
          fill="none" 
          stroke="white" 
          stroke-width="1" 
          opacity="0.3"/>
</svg>
