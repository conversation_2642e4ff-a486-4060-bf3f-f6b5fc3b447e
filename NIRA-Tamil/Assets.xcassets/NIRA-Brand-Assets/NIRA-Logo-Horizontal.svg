<?xml version="1.0" encoding="UTF-8"?>
<svg width="400" height="120" viewBox="0 0 400 120" xmlns="http://www.w3.org/2000/svg">
  <!-- NIRA Horizontal Logo - Icon + Text Layout -->
  
  <!-- Gradient Definitions -->
  <defs>
    <!-- Primary Radial Gradient -->
    <radialGradient id="niraGradientHoriz" cx="50%" cy="50%" r="50%">
      <stop offset="0%" style="stop-color:#4ECDC4;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#FF6B35;stop-opacity:1" />
    </radialGradient>
    
    <!-- Text Shadow Filter -->
    <filter id="textShadowHoriz" x="-50%" y="-50%" width="200%" height="200%">
      <feDropShadow dx="1" dy="1" stdDeviation="2" flood-color="#000000" flood-opacity="0.2"/>
    </filter>
  </defs>
  
  <!-- Icon Circle -->
  <circle cx="60" cy="60" r="55" 
          fill="url(#niraGradientHoriz)" 
          stroke="#FFE66D" 
          stroke-width="2"/>
  
  <!-- Tamil Script "நீ" in Icon -->
  <text x="60" y="75" 
        font-family="Tamil Sangam MN, Noto Sans Tamil, serif" 
        font-size="45" 
        font-weight="bold"
        fill="white" 
        text-anchor="middle" 
        filter="url(#textShadowHoriz)">நீ</text>
  
  <!-- NIRA Text -->
  <text x="140" y="50" 
        font-family="SF Pro Display, -apple-system, BlinkMacSystemFont, sans-serif" 
        font-size="36" 
        font-weight="700"
        fill="#FF6B35" 
        letter-spacing="2px">NIRA</text>
  
  <!-- Tamil Text -->
  <text x="140" y="80" 
        font-family="Tamil Sangam MN, Noto Sans Tamil, serif" 
        font-size="24" 
        font-weight="500"
        fill="#4ECDC4">தமிழ் கற்றல்</text>
  
  <!-- Subtitle -->
  <text x="140" y="100" 
        font-family="SF Pro Display, -apple-system, BlinkMacSystemFont, sans-serif" 
        font-size="14" 
        font-weight="400"
        fill="#6C757D" 
        letter-spacing="1px">HERITAGE LANGUAGE LEARNING</text>
</svg>
