//
//  SupabaseContentService.swift
//  NIRA-Tamil
//
//  Created by AI Assistant on 2025-01-19.
//  Copyright © 2025 Securight. All rights reserved.
//

import Foundation
import Supabase
import Combine

@MainActor
class SupabaseContentService: ObservableObject {
    static let shared = SupabaseContentService()

    // MARK: - Published Properties
    @Published var isLoading = false
    @Published var errorMessage: String?
    @Published var lessons: [TamilSupabaseLesson] = []
    @Published var currentLesson: TamilSupabaseLesson?

    // MARK: - Private Properties
    private let supabase: SupabaseClient
    private var cancellables = Set<AnyCancellable>()
    private let exerciseGenerator = PracticeExerciseGeneratorService()

    // MARK: - Initialization
    private init() {
        // Check if Supabase API keys are configured
        if APIKeys.supabaseConfigured {
            // Use real Supabase client
            self.supabase = SupabaseClient(
                supabaseURL: URL(string: APIKeys.supabaseURL)!,
                supabaseKey: APIKeys.supabaseAnonKey
            )
            print("🔗 SupabaseContentService initialized with real Supabase client")
        } else {
            // Use mock client for development
            print("⚠️ Using mock Supabase client - API keys not configured")
            self.supabase = SupabaseClient(
                supabaseURL: URL(string: "https://mock.supabase.co")!,
                supabaseKey: "mock-key"
            )
        }

        print("🔗 SupabaseContentService initialized with project: wnsorhbsucjguaoquhvr")
    }

    // MARK: - Public Methods

    /// Debug method to check lesson counts by level in Supabase
    func debugLessonCounts() async {
        guard APIKeys.supabaseConfigured else {
            print("🔄 Supabase not configured, skipping debug check")
            return
        }

        do {
            print("🔍 Checking lesson counts by level in Supabase...")

            let response: [TamilSupabaseLesson] = try await supabase
                .from("lessons")
                .select("level_code, lesson_number, title_english")
                .eq("is_active", value: true)
                .order("level_code")
                .order("lesson_number")
                .execute()
                .value

            // Group by level and count
            let groupedLessons = Dictionary(grouping: response, by: { $0.levelCode })

            print("📊 Lesson counts by level:")
            for level in ["A1", "A2", "B1", "B2", "C1", "C2"] {
                let count = groupedLessons[level]?.count ?? 0
                print("  \(level): \(count) lessons")

                if let lessons = groupedLessons[level], !lessons.isEmpty {
                    print("    First few: \(lessons.prefix(3).map { "L\($0.lessonNumber): \($0.titleEnglish)" }.joined(separator: ", "))")
                }
            }

        } catch {
            print("❌ Error checking lesson counts: \(error)")
        }
    }

    /// Fetch lessons for a specific CEFR level
    func fetchLessons(for level: CEFRLevel) async {
        await MainActor.run {
            isLoading = true
            errorMessage = nil
        }

        // Check if using mock data
        if !APIKeys.supabaseConfigured {
            print("🔄 Using mock lessons for level: \(level.rawValue)")

            // Return mock lessons to prevent crashes
            let mockLessons = createMockLessons(for: level)
            print("✅ Loaded \(mockLessons.count) mock lessons for level \(level.rawValue)")

            await MainActor.run {
                lessons = mockLessons
                isLoading = false
            }
            return
        }

        do {
            print("🔄 Fetching lessons for level: \(level.rawValue)")

            let response: [TamilSupabaseLesson] = try await supabase
                .from("lessons")
                .select("""
                    id,
                    lesson_number,
                    level_code,
                    title_english,
                    title_tamil,
                    title_romanization,
                    description_english,
                    description_tamil,
                    focus,
                    duration_minutes,
                    difficulty_score,
                    tags,
                    cultural_context,
                    is_active,
                    created_at,
                    updated_at
                """)
                .eq("level_code", value: level.rawValue)
                .eq("is_active", value: true)
                .order("lesson_number")
                .execute()
                .value

            await MainActor.run {
                lessons = response
            }
            print("✅ Fetched \(response.count) lessons for level \(level.rawValue)")

            // Debug: Show lesson details
            if response.isEmpty {
                print("⚠️ No lessons found for level \(level.rawValue) in Supabase")
            } else {
                print("📋 Lessons found for \(level.rawValue):")
                for lesson in response.prefix(3) {
                    print("  - Lesson \(lesson.lessonNumber): \(lesson.titleEnglish)")
                }
                if response.count > 3 {
                    print("  ... and \(response.count - 3) more lessons")
                }
            }

        } catch {
            let mockLessons = createMockLessons(for: level)
            await MainActor.run {
                errorMessage = "Failed to fetch lessons: \(error.localizedDescription)"
                lessons = mockLessons
            }
            print("❌ Error fetching lessons: \(error)")
            print("🔄 Using fallback mock lessons for level: \(level.rawValue)")
        }

        await MainActor.run {
            isLoading = false
        }
    }

    /// Fetch vocabulary for a specific lesson
    func fetchVocabulary(for lessonId: String) async -> [TamilSupabaseVocabulary] {
        do {
            print("🔄 Fetching vocabulary for lesson: \(lessonId)")

            let response: [TamilSupabaseVocabulary] = try await supabase
                .from("vocabulary")
                .select("*")
                .eq("lesson_id", value: lessonId)
                .order("vocab_id")
                .execute()
                .value

            print("✅ Fetched \(response.count) vocabulary items")
            return response

        } catch {
            print("❌ Error fetching vocabulary: \(error)")
            return []
        }
    }

    /// Fetch conversations for a specific lesson
    func fetchConversations(for lessonId: String) async -> [TamilSupabaseConversation] {
        do {
            print("🔄 Fetching conversations for lesson: \(lessonId)")

            let response: [TamilSupabaseConversation] = try await supabase
                .from("conversations")
                .select("*")
                .eq("lesson_id", value: lessonId)
                .order("conversation_id")
                .execute()
                .value

            print("✅ Fetched \(response.count) conversations")
            return response

        } catch {
            print("❌ Error fetching conversations: \(error)")
            return []
        }
    }

    /// Fetch conversation lines for a specific conversation
    func fetchConversationLines(for conversationId: String) async -> [TamilSupabaseConversationLine] {
        do {
            print("🔄 Fetching conversation lines for: \(conversationId)")

            let response: [TamilSupabaseConversationLine] = try await supabase
                .from("conversation_lines")
                .select("*")
                .eq("conversation_id", value: conversationId)
                .order("line_number", ascending: true)
                .execute()
                .value

            print("✅ Fetched \(response.count) conversation lines")
            return response

        } catch {
            print("❌ Error fetching conversation lines: \(error)")
            return []
        }
    }

    /// Fetch conversation with all its lines
    func fetchConversationWithLines(conversationId: String) async -> ConversationWithLines? {
        do {
            print("🔄 Fetching conversation with lines for: \(conversationId)")

            // Fetch conversation metadata
            let conversations: [TamilSupabaseConversation] = try await supabase
                .from("conversations")
                .select("*")
                .eq("conversation_id", value: conversationId)
                .execute()
                .value

            guard let conversation = conversations.first else {
                print("❌ No conversation found with ID: \(conversationId)")
                return nil
            }

            // Fetch conversation lines
            let lines = await fetchConversationLines(for: conversationId)

            let conversationWithLines = ConversationWithLines(conversation: conversation, lines: lines)
            print("✅ Fetched conversation with \(lines.count) lines")
            return conversationWithLines

        } catch {
            print("❌ Error fetching conversation with lines: \(error)")
            return nil
        }
    }

    /// Fetch all conversations with lines for a lesson
    func fetchConversationsWithLines(for lessonId: String) async -> [ConversationWithLines] {
        print("🔄 Fetching all conversations with lines for lesson: \(lessonId)")

        // First get all conversations for the lesson
        let conversations = await fetchConversations(for: lessonId)

        // Then fetch lines for each conversation
        var conversationsWithLines: [ConversationWithLines] = []

        for conversation in conversations {
            let lines = await fetchConversationLines(for: conversation.conversationId)
            conversationsWithLines.append(ConversationWithLines(conversation: conversation, lines: lines))
        }

        print("✅ Fetched \(conversationsWithLines.count) conversations with lines")
        return conversationsWithLines
    }

    /// Fetch grammar topics for a specific lesson
    func fetchGrammarTopics(for lessonId: String) async -> [TamilSupabaseGrammarTopic] {
        do {
            print("🔄 Fetching grammar topics for lesson: \(lessonId)")

            let response: [TamilSupabaseGrammarTopic] = try await supabase
                .from("grammar_topics")
                .select("*")
                .eq("lesson_id", value: lessonId)
                .order("grammar_id")
                .execute()
                .value

            print("✅ Fetched \(response.count) grammar topics")
            return response

        } catch {
            print("❌ Error fetching grammar topics: \(error)")
            return []
        }
    }

    /// Fetch grammar examples for a specific grammar topic
    func fetchGrammarExamples(for grammarTopicId: String) async -> [TamilSupabaseGrammarExample] {
        do {
            print("🔄 Fetching grammar examples for topic: \(grammarTopicId)")

            let response: [TamilSupabaseGrammarExample] = try await supabase
                .from("grammar_examples")
                .select("*")
                .eq("grammar_topic_id", value: grammarTopicId)
                .order("example_order")
                .execute()
                .value

            print("✅ Fetched \(response.count) grammar examples")
            return response

        } catch {
            print("❌ Error fetching grammar examples: \(error)")
            return []
        }
    }

    /// Convert Supabase grammar data to LessonGrammarPoint objects
    func convertToLessonGrammarPoints(_ grammarTopics: [TamilSupabaseGrammarTopic]) async -> [LessonGrammarPoint] {
        var grammarPoints: [LessonGrammarPoint] = []

        for topic in grammarTopics {
            // Fetch examples for this topic
            let examples = await fetchGrammarExamples(for: topic.id)

            // Convert examples to GrammarExample objects
            let grammarExamples = examples.map { example in
                GrammarExample(
                    english: example.exampleEnglish,
                    tamil: example.exampleTamil,
                    romanization: example.exampleRomanization,
                    audioURL: example.audioUrl
                )
            }

            // Create LessonGrammarPoint
            let grammarPoint = LessonGrammarPoint(
                rule: topic.titleEnglish,
                ruleTamil: topic.titleTamil,
                ruleRomanization: topic.titleRomanization,
                explanation: topic.explanation ?? topic.ruleEnglish,
                examples: grammarExamples,
                tips: topic.tips,
                commonMistakes: topic.commonMistakes,
                difficultyLevel: topic.difficultyLevel
            )

            grammarPoints.append(grammarPoint)
        }

        return grammarPoints
    }

    /// Fetch practice exercises for a specific lesson - now generates high-quality exercises with romanization
    func fetchPracticeExercises(for lessonId: String) async -> [TamilSupabasePracticeExercise] {
        print("🔄 Generating enhanced practice exercises for lesson: \(lessonId)")

        // Fetch lesson content to generate exercises from
        async let vocabulary = fetchVocabulary(for: lessonId)
        async let conversations = fetchConversations(for: lessonId)
        async let grammar = fetchGrammarTopics(for: lessonId)

        let (vocabData, conversationData, grammarData) = await (vocabulary, conversations, grammar)

        // Use enhanced generator for better quality exercises
        let enhancedGenerator = EnhancedPracticeExerciseGenerator.shared
        let generatedExercises = await enhancedGenerator.generateQualityPracticeExercises(
            from: vocabData,
            conversations: conversationData,
            grammar: grammarData,
            lessonId: lessonId
        )

        print("✅ Generated \(generatedExercises.count) enhanced practice exercises with romanization and pronunciation")
        return generatedExercises
    }

    /// Fetch complete lesson with all content
    func fetchCompleteLesson(lessonId: String) async -> CompleteTamilLesson? {
        do {
            print("🔄 Fetching complete lesson: \(lessonId)")

            // Fetch lesson details
            let lessonResponse: [TamilSupabaseLesson] = try await supabase
                .from("lessons")
                .select("*")
                .eq("id", value: lessonId)
                .execute()
                .value

            guard let lesson = lessonResponse.first else {
                print("❌ Lesson not found: \(lessonId)")
                return nil
            }

            // Fetch all content in parallel
            async let vocabulary = fetchVocabulary(for: lessonId)
            async let conversations = fetchConversations(for: lessonId)
            async let grammarTopics = fetchGrammarTopics(for: lessonId)
            async let practiceExercises = fetchPracticeExercises(for: lessonId)

            let completeLesson = CompleteTamilLesson(
                lesson: lesson,
                vocabulary: await vocabulary,
                conversations: await conversations,
                grammarTopics: await grammarTopics,
                practiceExercises: await practiceExercises
            )

            print("✅ Fetched complete lesson with all content")
            return completeLesson

        } catch {
            print("❌ Error fetching complete lesson: \(error)")
            return nil
        }
    }

    // MARK: - Mock Data for Development

    /// Create mock lessons for development when API keys are not configured
    private func createMockLessons(for level: CEFRLevel) -> [TamilSupabaseLesson] {
        let currentDate = ISO8601DateFormatter().string(from: Date())

        switch level {
        case .a1:
            return [
                TamilSupabaseLesson(
                    id: UUID().uuidString,
                    lessonNumber: 1,
                    levelCode: "A1",
                    titleEnglish: "Basic Greetings",
                    titleTamil: "Learn essential greetings like hello, goodbye, and thank you in Tamil",
                    titleRomanization: "Adippadai Vaazhthugal",
                    descriptionEnglish: "Learn basic Tamil greetings and introductions",
                    descriptionTamil: "அடிப்படை தமிழ் வாழ்த்துகள் மற்றும் அறிமுகங்களைக் கற்றுக்கொள்ளுங்கள்",
                    focus: "Greetings, introductions, basic politeness",
                    durationMinutes: 20,
                    difficultyScore: 1,
                    prerequisites: [],
                    tags: ["greetings", "basic", "introductions"],
                    culturalContext: "Tamil greeting customs and social etiquette",
                    isActive: true,
                    createdAt: currentDate,
                    updatedAt: currentDate
                ),
                TamilSupabaseLesson(
                    id: UUID().uuidString,
                    lessonNumber: 2,
                    levelCode: "A1",
                    titleEnglish: "Numbers and Counting",
                    titleTamil: "Master Tamil numbers 1-100 and basic color vocabulary for daily use",
                    titleRomanization: "Engal Mattrum Ennuthal",
                    descriptionEnglish: "Learn Tamil numbers from 1 to 100",
                    descriptionTamil: "1 முதல் 100 வரையிலான தமிழ் எண்களைக் கற்றுக்கொள்ளுங்கள்",
                    focus: "Numbers, counting, basic arithmetic",
                    durationMinutes: 25,
                    difficultyScore: 1,
                    prerequisites: [],
                    tags: ["numbers", "counting", "arithmetic"],
                    culturalContext: "Tamil number system and traditional counting",
                    isActive: true,
                    createdAt: currentDate,
                    updatedAt: currentDate
                )
            ]
        case .a2:
            return [
                TamilSupabaseLesson(
                    id: UUID().uuidString,
                    lessonNumber: 1,
                    levelCode: "A2",
                    titleEnglish: "Family and Relationships",
                    titleTamil: "Expand family vocabulary with extended relatives and relationship terms",
                    titleRomanization: "Kudumbam Mattrum Uravugal",
                    descriptionEnglish: "Learn about family members and relationships",
                    descriptionTamil: "குடும்ப உறுப்பினர்கள் மற்றும் உறவுகளைப் பற்றி அறியுங்கள்",
                    focus: "Family vocabulary, relationships, social connections",
                    durationMinutes: 30,
                    difficultyScore: 2,
                    prerequisites: [],
                    tags: ["family", "relationships", "social"],
                    culturalContext: "Tamil family structure and traditions",
                    isActive: true,
                    createdAt: currentDate,
                    updatedAt: currentDate
                )
            ]
        default:
            return [
                TamilSupabaseLesson(
                    id: UUID().uuidString,
                    lessonNumber: 1,
                    levelCode: level.rawValue,
                    titleEnglish: "Advanced Tamil",
                    titleTamil: "Develop advanced Tamil communication and analytical skills",
                    titleRomanization: "Mempatta Tamil",
                    descriptionEnglish: "Advanced Tamil language concepts",
                    descriptionTamil: "மேம்பட்ட தமிழ் மொழி கருத்துகள்",
                    focus: "Advanced concepts and cultural nuances",
                    durationMinutes: 40,
                    difficultyScore: level.rawValue == "B1" ? 3 : level.rawValue == "B2" ? 4 : level.rawValue == "C1" ? 5 : 6,
                    prerequisites: [],
                    tags: ["advanced", "culture", "nuances"],
                    culturalContext: "Deep Tamil cultural understanding",
                    isActive: true,
                    createdAt: currentDate,
                    updatedAt: currentDate
                )
            ]
        }
    }
}