import Foundation
import AVFoundation

/// Real Audio Fix Service that actually updates the Supabase database
/// This service generates working audio URLs and updates the database
@MainActor
class RealAudioFixService: ObservableObject {
    static let shared = RealAudioFixService()
    
    @Published var isFixing = false
    @Published var fixProgress = 0.0
    @Published var statusMessage = ""
    @Published var fixedCount = 0
    
    private let supabaseService = SupabaseContentService.shared
    
    private init() {}
    
    /// Fix Basic Greetings audio URLs with real database updates
    func fixBasicGreetingsAudio() async throws {
        isFixing = true
        fixProgress = 0.0
        fixedCount = 0
        statusMessage = "Loading Basic Greetings vocabulary..."
        
        // Load vocabulary
        let vocabularyItems = await supabaseService.fetchVocabulary(for: "7b8c60af-dd2f-4754-9363-ab09a5bcea95")
        
        statusMessage = "Fixing audio URLs for \(vocabularyItems.count) items..."
        
        let totalItems = vocabularyItems.count
        
        for (index, vocabulary) in vocabularyItems.enumerated() {
            do {
                // Create working audio URLs
                let wordAudioURL = createWorkingAudioURL(
                    text: vocabulary.tamilTranslation,
                    type: "word",
                    vocabId: vocabulary.vocabId
                )
                
                var sentenceAudioURL: String?
                if let exampleTamil = vocabulary.exampleSentenceTamil, !exampleTamil.isEmpty {
                    sentenceAudioURL = createWorkingAudioURL(
                        text: exampleTamil,
                        type: "sentence", 
                        vocabId: vocabulary.vocabId
                    )
                }
                
                // Actually update the database
                try await updateVocabularyInDatabase(
                    vocabularyId: vocabulary.id.uuidString,
                    wordAudioURL: wordAudioURL,
                    sentenceAudioURL: sentenceAudioURL
                )
                
                fixedCount += 1
                fixProgress = Double(index + 1) / Double(totalItems)
                statusMessage = "Fixed \(index + 1)/\(totalItems) audio URLs"
                
                // Small delay
                try await Task.sleep(nanoseconds: 200_000_000) // 0.2 seconds
                
            } catch {
                print("❌ Failed to fix audio for \(vocabulary.englishWord): \(error)")
            }
        }
        
        isFixing = false
        statusMessage = "Completed! Fixed \(fixedCount)/\(totalItems) audio URLs"
    }
    
    /// Create working audio URL using Google Translate TTS
    private func createWorkingAudioURL(text: String, type: String, vocabId: String) -> String {
        // Use Google Translate TTS (free tier) for immediate working audio
        let encodedText = text.addingPercentEncoding(withAllowedCharacters: .urlQueryAllowed) ?? text
        
        // Google Translate TTS URL that actually works
        return "https://translate.google.com/translate_tts?ie=UTF-8&tl=ta&client=tw-ob&q=\(encodedText)"
    }
    
    /// Actually update the vocabulary record in Supabase database
    private func updateVocabularyInDatabase(
        vocabularyId: String,
        wordAudioURL: String,
        sentenceAudioURL: String?
    ) async throws {
        print("📝 Updating vocabulary \(vocabularyId) in database...")
        print("   Word Audio: \(wordAudioURL)")
        if let sentenceURL = sentenceAudioURL {
            print("   Sentence Audio: \(sentenceURL)")
        }
        
        // Use Supabase REST API to update the record
        let updateQuery = """
        UPDATE vocabulary 
        SET audio_word_url = '\(wordAudioURL)',
            audio_sentence_url = '\(sentenceAudioURL ?? "")'
        WHERE id = '\(vocabularyId)'
        """
        
        // Execute the update using Supabase API
        let url = URL(string: "https://wnsorhbsucjguaoquhvr.supabase.co/rest/v1/rpc/execute_sql")!
        var request = URLRequest(url: url)
        request.httpMethod = "POST"
        request.setValue("application/json", forHTTPHeaderField: "Content-Type")
        request.setValue("Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Induc29yaGJzdWNqZ3Vhb3F1aHZyIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTAyNzUzOTgsImV4cCI6MjA2NTg1MTM5OH0.HS2BW4qh6gySIUBz7fQJju0rUMGVz5ueNrW_lzk9hbs", forHTTPHeaderField: "Authorization")
        request.setValue("eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Induc29yaGJzdWNqZ3Vhb3F1aHZyIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTAyNzUzOTgsImV4cCI6MjA2NTg1MTM5OH0.HS2BW4qh6gySIUBz7fQJju0rUMGVz5ueNrW_lzk9hbs", forHTTPHeaderField: "apikey")
        
        let requestBody = ["sql": updateQuery]
        request.httpBody = try JSONSerialization.data(withJSONObject: requestBody)
        
        let (_, response) = try await URLSession.shared.data(for: request)
        
        guard let httpResponse = response as? HTTPURLResponse else {
            throw AudioFixError.invalidResponse
        }
        
        guard httpResponse.statusCode == 200 || httpResponse.statusCode == 204 else {
            throw AudioFixError.databaseUpdateFailed(httpResponse.statusCode)
        }
        
        print("✅ Successfully updated vocabulary \(vocabularyId) in database")
    }
    
    /// Alternative method using direct table update
    private func updateVocabularyDirectly(
        vocabularyId: String,
        wordAudioURL: String,
        sentenceAudioURL: String?
    ) async throws {
        let url = URL(string: "https://wnsorhbsucjguaoquhvr.supabase.co/rest/v1/vocabulary?id=eq.\(vocabularyId)")!
        var request = URLRequest(url: url)
        request.httpMethod = "PATCH"
        request.setValue("application/json", forHTTPHeaderField: "Content-Type")
        request.setValue("Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Induc29yaGJzdWNqZ3Vhb3F1aHZyIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTAyNzUzOTgsImV4cCI6MjA2NTg1MTM5OH0.HS2BW4qh6gySIUBz7fQJju0rUMGVz5ueNrW_lzk9hbs", forHTTPHeaderField: "Authorization")
        request.setValue("eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Induc29yaGJzdWNqZ3Vhb3F1aHZyIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTAyNzUzOTgsImV4cCI6MjA2NTg1MTM5OH0.HS2BW4qh6gySIUBz7fQJju0rUMGVz5ueNrW_lzk9hbs", forHTTPHeaderField: "apikey")
        
        let updateData: [String: Any] = [
            "audio_word_url": wordAudioURL,
            "audio_sentence_url": sentenceAudioURL ?? ""
        ]
        
        request.httpBody = try JSONSerialization.data(withJSONObject: updateData)
        
        let (_, response) = try await URLSession.shared.data(for: request)
        
        guard let httpResponse = response as? HTTPURLResponse else {
            throw AudioFixError.invalidResponse
        }
        
        guard httpResponse.statusCode == 200 || httpResponse.statusCode == 204 else {
            throw AudioFixError.databaseUpdateFailed(httpResponse.statusCode)
        }
        
        print("✅ Successfully updated vocabulary \(vocabularyId) directly")
    }
    
    /// Test the fix by checking if URLs are working
    func testFixedAudioURLs() async -> AudioTestReport {
        statusMessage = "Testing fixed audio URLs..."
        
        let vocabularyItems = await supabaseService.fetchVocabulary(for: "7b8c60af-dd2f-4754-9363-ab09a5bcea95")
        
        var workingURLs = 0
        var brokenURLs = 0
        var testResults: [String] = []
        
        for vocabulary in vocabularyItems {
            // Test word URL
            if let wordURL = vocabulary.audioWordUrl {
                let isWorking = await testAudioURL(wordURL)
                if isWorking {
                    workingURLs += 1
                    testResults.append("✅ \(vocabulary.englishWord) - Word audio working")
                } else {
                    brokenURLs += 1
                    testResults.append("❌ \(vocabulary.englishWord) - Word audio broken")
                }
            }
            
            // Test sentence URL
            if let sentenceURL = vocabulary.audioSentenceUrl {
                let isWorking = await testAudioURL(sentenceURL)
                if isWorking {
                    workingURLs += 1
                    testResults.append("✅ \(vocabulary.englishWord) - Sentence audio working")
                } else {
                    brokenURLs += 1
                    testResults.append("❌ \(vocabulary.englishWord) - Sentence audio broken")
                }
            }
        }
        
        return AudioTestReport(
            totalTested: workingURLs + brokenURLs,
            workingURLs: workingURLs,
            brokenURLs: brokenURLs,
            testResults: testResults
        )
    }
    
    /// Test if an audio URL is working
    private func testAudioURL(_ urlString: String) async -> Bool {
        guard let url = URL(string: urlString) else { return false }
        
        do {
            let (_, response) = try await URLSession.shared.data(from: url)
            if let httpResponse = response as? HTTPURLResponse {
                return httpResponse.statusCode == 200
            }
            return false
        } catch {
            return false
        }
    }
}

// MARK: - Supporting Types

enum AudioFixError: Error, LocalizedError {
    case invalidResponse
    case databaseUpdateFailed(Int)
    case networkError
    
    var errorDescription: String? {
        switch self {
        case .invalidResponse:
            return "Invalid response from server"
        case .databaseUpdateFailed(let code):
            return "Database update failed with code: \(code)"
        case .networkError:
            return "Network error occurred"
        }
    }
}

struct AudioTestReport {
    let totalTested: Int
    let workingURLs: Int
    let brokenURLs: Int
    let testResults: [String]
    
    var successRate: Double {
        guard totalTested > 0 else { return 0 }
        return Double(workingURLs) / Double(totalTested)
    }
    
    var summary: String {
        return "Tested \(totalTested) URLs: \(workingURLs) working, \(brokenURLs) broken"
    }
}
