import Foundation
import Combine
import CoreLocation
import SwiftUI

// MARK: - Real-time Panchang Service
@MainActor
class RealTimePanchangService: ObservableObject {
    static let shared = RealTimePanchangService()
    
    // MARK: - Published Properties
    @Published var todayPanchang: DailyPanchang?
    @Published var currentMonthPanchang: [DailyPanchang] = []
    @Published var isLoading = false
    @Published var error: Error?
    @Published var lastUpdateTime: Date?
    @Published var cacheStatus: PanchangCacheStatus = .unknown
    
    // MARK: - Private Properties
    private let apiClient = PanchangAPIClient.shared
    private let supabaseClient = NIRASupabaseClient.shared
    private let locationManager = LocationManager()
    private var cancellables = Set<AnyCancellable>()
    
    // Default location (Chennai, Tamil Nadu)
    private let defaultLocation = CLLocation(latitude: 13.0827, longitude: 80.2707)

    // Rate limiting to prevent request cancellation
    private var lastRequestTime: Date = Date.distantPast
    private let minimumRequestInterval: TimeInterval = 0.5 // 500ms between requests

    // Background task management to prevent cancellation
    private var backgroundTasks: [String: Task<Void, Never>] = [:]
    
    private init() {
        setupDailyUpdates()
        setupLocationUpdates()
    }
    
    // MARK: - Public Methods
    
    func loadTodayPanchang() async {
        await loadPanchangForDate(Date())
    }
    
    func loadPanchangForDate(_ date: Date) async {
        isLoading = true
        error = nil

        do {
            let panchang = try await getPanchangForDate(date)

            if Calendar.current.isDateInToday(date) {
                todayPanchang = panchang
            }

            lastUpdateTime = Date()
            print("✅ Successfully loaded panchang for \(date)")

        } catch {
            self.error = error
            print("❌ Failed to load panchang for \(date): \(error)")
            // No fallback - user needs real data
        }

        isLoading = false
    }
    
    func loadMonthPanchang(for date: Date) async {
        isLoading = true
        error = nil

        let monthDates = getMonthDates(for: date)
        var monthPanchang: [DailyPanchang] = []

        for monthDate in monthDates {
            if let panchang = try? await getPanchangForDate(monthDate) {
                monthPanchang.append(panchang)
            }
        }

        currentMonthPanchang = monthPanchang.sorted { $0.date < $1.date }
        lastUpdateTime = Date()
        print("✅ Successfully loaded month panchang: \(monthPanchang.count) days")

        isLoading = false
    }

    func getMonthlyPanchang(for date: Date) async throws -> [DailyPanchang] {
        // Use unstructured task to prevent cancellation from parent context
        return try await withCheckedThrowingContinuation { continuation in
            Task.detached {
                do {
                    let result = try await self.getMonthlyPanchangInternal(for: date)
                    continuation.resume(returning: result)
                } catch {
                    continuation.resume(throwing: error)
                }
            }
        }
    }

    private func getMonthlyPanchangInternal(for date: Date) async throws -> [DailyPanchang] {
        let calendar = Calendar.current
        let startOfMonth = calendar.dateInterval(of: .month, for: date)?.start ?? date
        let endOfMonth = calendar.dateInterval(of: .month, for: date)?.end ?? date

        var monthlyData: [DailyPanchang] = []
        var currentDate = startOfMonth

        // Process dates sequentially with delays to avoid cancellation
        while currentDate < endOfMonth {
            do {
                if let panchang = try await getPanchangForDate(currentDate) {
                    monthlyData.append(panchang)
                }

                // Add small delay between requests to avoid overwhelming the API
                try await Task.sleep(nanoseconds: 300_000_000) // 0.3 seconds

            } catch {
                print("❌ Failed to load panchang for \(currentDate): \(error)")
                // Continue with next date even if one fails
            }

            currentDate = calendar.date(byAdding: .day, value: 1, to: currentDate) ?? currentDate
        }

        return monthlyData.sorted { $0.date < $1.date }
    }

    // Lighter method to load just a week of data
    func getWeeklyPanchang(for date: Date) async throws -> [DailyPanchang] {
        let calendar = Calendar.current
        let startOfWeek = calendar.dateInterval(of: .weekOfYear, for: date)?.start ?? date

        var weeklyData: [DailyPanchang] = []
        var currentDate = startOfWeek

        // Load only 7 days
        for _ in 0..<7 {
            do {
                try Task.checkCancellation()

                if let panchang = try await getPanchangForDate(currentDate) {
                    weeklyData.append(panchang)
                }

                // Small delay between requests
                try await Task.sleep(nanoseconds: 200_000_000) // 0.2 seconds

            } catch is CancellationError {
                print("⚠️ Weekly panchang loading cancelled")
                break
            } catch {
                print("❌ Failed to load panchang for \(currentDate): \(error)")
            }

            currentDate = calendar.date(byAdding: .day, value: 1, to: currentDate) ?? currentDate
        }

        return weeklyData.sorted { $0.date < $1.date }
    }

    func getPanchangForDate(_ date: Date) async throws -> DailyPanchang? {
        // First check cache
        if let cachedPanchang = await getCachedPanchang(for: date) {
            print("📱 Using cached panchang for \(date)")
            return cachedPanchang
        }

        // If not in cache, fetch from API with retry logic
        var retryCount = 0
        let maxRetries = 2

        while retryCount <= maxRetries {
            do {
                // Check if task is cancelled before making request
                try Task.checkCancellation()

                // Rate limiting to prevent overwhelming the API
                let now = Date()
                let timeSinceLastRequest = now.timeIntervalSince(lastRequestTime)
                if timeSinceLastRequest < minimumRequestInterval {
                    let sleepTime = minimumRequestInterval - timeSinceLastRequest
                    try await Task.sleep(nanoseconds: UInt64(sleepTime * 1_000_000_000))
                }
                lastRequestTime = Date()

                let location = getCurrentLocation()
                let apiResponse = try await apiClient.fetchPanchang(for: date, location: location)
                let panchang = convertAPIResponseToPanchang(apiResponse, date: date, location: location)

                // Cache the result
                await cachePanchang(panchang)

                return panchang

            } catch is CancellationError {
                print("⚠️ Panchang request cancelled for \(date)")
                throw CancellationError()
            } catch {
                retryCount += 1
                print("❌ Failed to fetch panchang from API (attempt \(retryCount)): \(error)")

                if retryCount <= maxRetries {
                    // Wait before retrying
                    try await Task.sleep(nanoseconds: UInt64(retryCount) * 1_000_000_000) // 1, 2 seconds
                } else {
                    throw error
                }
            }
        }

        return nil
    }
    
    func refreshData() async {
        await loadTodayPanchang()
        await loadMonthPanchang(for: Date())
    }

    // Background loading methods that won't be cancelled by SwiftUI
    func loadMonthPanchangInBackground(for date: Date) {
        let taskKey = "monthly_\(date.timeIntervalSince1970)"

        // Cancel any existing task for this month
        backgroundTasks[taskKey]?.cancel()

        // Create new unstructured task
        backgroundTasks[taskKey] = Task.detached { [weak self] in
            guard let self = self else { return }

            do {
                let monthlyData = try await self.getMonthlyPanchang(for: date)

                await MainActor.run {
                    self.currentMonthPanchang = monthlyData
                    self.lastUpdateTime = Date()
                    self.isLoading = false
                }
            } catch {
                await MainActor.run {
                    self.error = error
                    self.isLoading = false
                }
                print("❌ Background monthly loading failed: \(error)")
            }

            // Clean up task reference
            await MainActor.run {
                _ = self.backgroundTasks.removeValue(forKey: taskKey)
            }
        }
    }

    func loadTodayPanchangInBackground() {
        let taskKey = "today"

        // Cancel any existing task
        backgroundTasks[taskKey]?.cancel()

        // Create new unstructured task
        backgroundTasks[taskKey] = Task.detached { [weak self] in
            guard let self = self else { return }

            do {
                let todayData = try await self.getPanchangForDate(Date())

                await MainActor.run {
                    self.todayPanchang = todayData
                    self.lastUpdateTime = Date()
                    self.isLoading = false
                }
            } catch {
                await MainActor.run {
                    self.error = error
                    self.isLoading = false
                }
                print("❌ Background today loading failed: \(error)")
            }

            // Clean up task reference
            await MainActor.run {
                _ = self.backgroundTasks.removeValue(forKey: taskKey)
            }
        }
    }
    
    func preloadNextDays(count: Int = 7) async {
        let today = Date()
        
        for i in 1...count {
            if let futureDate = Calendar.current.date(byAdding: .day, value: i, to: today) {
                _ = try? await getPanchangForDate(futureDate)
            }
        }
    }
    
    // MARK: - Private Methods
    
    private func getCurrentLocation() -> CLLocation {
        return locationManager.currentLocation ?? defaultLocation
    }
    
    private func getCachedPanchang(for date: Date) async -> DailyPanchang? {
        do {
            let dateString = DateFormatter.databaseDateFormatter.string(from: date)
            
            let response: [SupabasePanchangRecord] = try await supabaseClient.client
                .from("daily_panchang")
                .select("*")
                .eq("date", value: dateString)
                .limit(1)
                .execute()
                .value
            
            guard let record = response.first else { return nil }
            
            // Check if cache is still fresh (less than 24 hours old)
            let cacheAge = Date().timeIntervalSince(record.updatedAt)
            if cacheAge > 24 * 60 * 60 { // 24 hours
                print("⚠️ Cache expired for \(date)")
                return nil
            }
            
            return convertSupabaseRecordToPanchang(record)
            
        } catch {
            print("❌ Error fetching cached panchang: \(error)")
            return nil
        }
    }
    
    private func cachePanchang(_ panchang: DailyPanchang) async {
        do {
            let record = convertPanchangToSupabaseRecord(panchang)
            let dateString = DateFormatter.databaseDateFormatter.string(from: panchang.date)

            let _: [SupabasePanchangRecord] = try await supabaseClient.client
                .from("daily_panchang")
                .upsert(record, onConflict: "date")
                .execute()
                .value

            print("✅ Cached panchang for \(dateString)")

        } catch {
            print("❌ Error caching panchang: \(error)")
        }
    }
    
    private func convertAPIResponseToPanchang(_ response: PanchangResponse, date: Date, location: CLLocation) -> DailyPanchang {
        let locationInfo = LocationInfo(from: location)
        
        // Get current elements (first in array)
        let currentTithi = response.data.tithi.first!
        let currentNakshatra = response.data.nakshatra.first!
        let currentYoga = response.data.yoga.first!
        let currentKarana = response.data.karana.first!

        // Convert API response to our models
        let tamilDate = createTamilDate(from: response, date: date, currentTithi: currentTithi)
        let sunTimes = createSunTimes(from: response, date: date)

        let tithi = createTithi(from: currentTithi)
        let nakshatra = createNakshatra(from: currentNakshatra)
        let yoga = createYoga(from: currentYoga)
        let karana = createKarana(from: currentKarana)
        let lunarMonth = createLunarMonth(from: response.data.vaara)
        let season = createSeason(from: date)
        
        // Generate muhurat timings
        let muhurat = generateMuhuratTimings(for: date, sunTimes: sunTimes)
        let inauspiciousTimes = generateInauspiciousTimings(for: date, sunTimes: sunTimes)
        
        return DailyPanchang(
            date: date,
            location: locationInfo,
            tamilDate: tamilDate,
            sunTimes: sunTimes,
            moonTimes: MoonTimes(
                moonrise: parseTimeString(response.data.moonrise, date: date),
                moonset: parseTimeString(response.data.moonset, date: date),
                phase: MoonPhase.newMoon, // Default for now
                illumination: 50.0 // Default for now
            ),
            tithi: tithi,
            nakshatra: nakshatra,
            yoga: yoga,
            karana: karana,
            lunarMonth: lunarMonth,
            season: season,
            muhurat: muhurat,
            inauspiciousTimes: inauspiciousTimes,
            festivals: [], // Will be populated from database
            significance: generateSignificance(tithi: tithi, nakshatra: nakshatra)
        )
    }
    
    private func createTamilDate(from response: PanchangResponse, date: Date, currentTithi: TithiData) -> PanchangTamilDate {
        // Convert Gregorian date to Tamil calendar
        let calendar = Calendar.current
        let month = calendar.component(.month, from: date)
        let day = calendar.component(.day, from: date)
        let year = calendar.component(.year, from: date)
        
        // Map to Tamil month (simplified mapping)
        let tamilMonth = mapToTamilMonth(gregorianMonth: month)
        let paksha = currentTithi.paksha.contains("Shukla") ? Paksha.shukla : Paksha.krishna
        let season = mapToTamilSeason(month: month)

        return PanchangTamilDate(
            day: day,
            month: tamilMonth,
            year: year + 1181, // Approximate Tamil year conversion
            paksha: paksha,
            season: season,
            era: .sakaEra
        )
    }
    
    private func createSunTimes(from response: PanchangResponse, date: Date) -> SunTimes {
        let calendar = Calendar.current
        _ = calendar.dateComponents([.year, .month, .day], from: date)
        
        let sunriseTime = parseTimeString(response.data.sunrise, date: date) ?? date
        let sunsetTime = parseTimeString(response.data.sunset, date: date) ?? date
        
        return SunTimes(
            sunrise: sunriseTime,
            sunset: sunsetTime,
            noon: calendar.date(bySettingHour: 12, minute: 0, second: 0, of: date),
            twilightBegin: calendar.date(byAdding: .minute, value: -30, to: sunriseTime),
            twilightEnd: calendar.date(byAdding: .minute, value: 30, to: sunsetTime)
        )
    }
    
    private func createTithi(from tithiData: TithiData) -> Tithi {
        return Tithi(
            number: tithiData.id,
            name: tithiData.name,
            tamilName: getTamilTithiName(tithiData.name),
            paksha: tithiData.paksha.contains("Shukla") ? .shukla : .krishna,
            completionTime: parseTimeString(tithiData.end),
            percentage: 50.0, // Default for now
            lord: getTithiLord(tithiData.id),
            significance: getTithiSignificance(tithiData.id)
        )
    }
    
    private func createNakshatra(from nakshatraData: NakshatraData) -> Nakshatra {
        return Nakshatra(
            number: nakshatraData.id,
            name: nakshatraData.name,
            tamilName: getTamilNakshatraName(nakshatraData.name),
            lord: nakshatraData.lord.name,
            tamilLord: getTamilNakshatraLord(nakshatraData.id),
            startTime: parseTimeString(nakshatraData.start) ?? Date(),
            endTime: parseTimeString(nakshatraData.end) ?? Date(),
            percentage: 50.0, // Default for now
            pada: nil,
            significance: getNakshatraSignificance(nakshatraData.id)
        )
    }
    
    private func createYoga(from yogaData: YogaData) -> Yoga {
        return Yoga(
            number: yogaData.id,
            name: yogaData.name,
            tamilName: getTamilYogaName(yogaData.name),
            completionTime: parseTimeString(yogaData.end),
            percentage: 50.0, // Default for now
            significance: getYogaSignificance(yogaData.id)
        )
    }
    
    private func createKarana(from karanaData: KaranaData) -> Karana {
        return Karana(
            number: karanaData.id,
            name: karanaData.name,
            tamilName: getTamilKaranaName(karanaData.name),
            completionTime: parseTimeString(karanaData.end),
            percentage: 50.0, // Default for now
            type: .movable
        )
    }
    
    private func createLunarMonth(from dateString: String) -> LunarMonth {
        // Create a basic lunar month from date - this would be enhanced with proper lunar calculations
        let calendar = Calendar.current
        let month = calendar.component(.month, from: Date())

        return LunarMonth(
            number: month,
            name: getLunarMonthName(month),
            tamilName: getTamilLunarMonthName(getLunarMonthName(month)),
            fullName: getLunarMonthName(month),
            isAdhika: false,
            isKshaya: false
        )
    }

    private func createSeason(from date: Date) -> Season {
        let calendar = Calendar.current
        let month = calendar.component(.month, from: date)
        let seasonNumber = ((month - 1) / 3) + 1
        let seasonName = getSeasonName(seasonNumber)

        return Season(
            number: seasonNumber,
            name: seasonName,
            tamilName: getTamilSeasonName(seasonName),
            description: getSeasonDescription(seasonName)
        )
    }
    
    private func generateMuhuratTimings(for date: Date, sunTimes: SunTimes) -> [Muhurat] {
        var muhurats: [Muhurat] = []
        let calendar = Calendar.current
        
        // Brahma Muhurat (1.5 hours before sunrise)
        if let brahmaMuhuratStart = calendar.date(byAdding: .minute, value: -90, to: sunTimes.sunrise) {
            muhurats.append(Muhurat(
                type: .brahmaMuhurat,
                name: "Brahma Muhurat",
                tamilName: "பிரம்ம முகூர்த்தம்",
                startTime: brahmaMuhuratStart,
                endTime: sunTimes.sunrise,
                significance: "Most auspicious time for spiritual practices",
                isAuspicious: true,
                activities: ["Meditation", "Prayer", "Study", "Yoga"]
            ))
        }
        
        // Abhijit Muhurat (noon ± 24 minutes)
        if let noon = calendar.date(bySettingHour: 12, minute: 0, second: 0, of: date),
           let abhijitStart = calendar.date(byAdding: .minute, value: -24, to: noon),
           let abhijitEnd = calendar.date(byAdding: .minute, value: 24, to: noon) {
            muhurats.append(Muhurat(
                type: .abhijitMuhurat,
                name: "Abhijit Muhurat",
                tamilName: "அபிஜித் முகூர்த்தம்",
                startTime: abhijitStart,
                endTime: abhijitEnd,
                significance: "Victory time, good for important tasks",
                isAuspicious: true,
                activities: ["Business", "Travel", "Important meetings"]
            ))
        }
        
        return muhurats
    }
    
    private func generateInauspiciousTimings(for date: Date, sunTimes: SunTimes) -> [InauspiciousTime] {
        var inauspiciousTimes: [InauspiciousTime] = []
        
        // Rahu Kalam calculation (varies by day of week)
        let weekday = Calendar.current.component(.weekday, from: date)
        let rahuKalamTiming = calculateRahuKalam(weekday: weekday, sunTimes: sunTimes)
        
        inauspiciousTimes.append(InauspiciousTime(
            type: .rahuKalam,
            name: "Rahu Kalam",
            tamilName: "ராகு காலம்",
            startTime: rahuKalamTiming.start,
            endTime: rahuKalamTiming.end,
            warning: "Avoid starting new ventures during this time",
            avoidActivities: ["New business", "Travel", "Important ceremonies"]
        ))
        
        return inauspiciousTimes
    }

    // MARK: - Helper Methods

    private func getLunarMonthName(_ month: Int) -> String {
        let months = ["Chaitra", "Vaisakha", "Jyaistha", "Ashadha", "Shravana", "Bhadrapada", "Ashwin", "Kartik", "Agrahayana", "Pausha", "Magha", "Phalguna"]
        return months[(month - 1) % 12]
    }

    private func getSeasonName(_ seasonNumber: Int) -> String {
        let seasons = ["Spring", "Summer", "Monsoon", "Winter"]
        return seasons[(seasonNumber - 1) % 4]
    }
    
    private func getMonthDates(for date: Date) -> [Date] {
        let calendar = Calendar.current
        guard let monthInterval = calendar.dateInterval(of: .month, for: date) else { return [] }
        
        var dates: [Date] = []
        var currentDate = monthInterval.start
        
        while currentDate < monthInterval.end {
            dates.append(currentDate)
            currentDate = calendar.date(byAdding: .day, value: 1, to: currentDate) ?? currentDate
        }
        
        return dates
    }
    
    private func setupDailyUpdates() {
        Timer.publish(every: 3600, on: .main, in: .common) // Every hour
            .autoconnect()
            .sink { [weak self] _ in
                Task {
                    await self?.checkForDailyUpdate()
                }
            }
            .store(in: &cancellables)
    }
    
    private func setupLocationUpdates() {
        locationManager.$currentLocation
            .compactMap { $0 }
            .sink { [weak self] _ in
                Task {
                    await self?.refreshData()
                }
            }
            .store(in: &cancellables)
    }
    
    private func checkForDailyUpdate() async {
        let calendar = Calendar.current
        let now = Date()
        
        // Check if it's a new day and time is after midnight
        if let lastUpdate = lastUpdateTime,
           !calendar.isDate(lastUpdate, inSameDayAs: now),
           calendar.component(.hour, from: now) >= 0 {
            await refreshData()
        }
    }
}

// MARK: - Supporting Types

enum PanchangCacheStatus {
    case unknown
    case fresh
    case stale
    case expired
    case error
}

// MARK: - Location Manager

class LocationManager: NSObject, ObservableObject, CLLocationManagerDelegate {
    @Published var currentLocation: CLLocation?
    private let locationManager = CLLocationManager()
    
    override init() {
        super.init()
        locationManager.delegate = self
        locationManager.desiredAccuracy = kCLLocationAccuracyKilometer
        requestLocationPermission()
    }
    
    private func requestLocationPermission() {
        switch locationManager.authorizationStatus {
        case .notDetermined:
            locationManager.requestWhenInUseAuthorization()
        case .authorizedWhenInUse, .authorizedAlways:
            locationManager.requestLocation()
        default:
            break
        }
    }
    
    func locationManager(_ manager: CLLocationManager, didUpdateLocations locations: [CLLocation]) {
        currentLocation = locations.last
    }
    
    func locationManager(_ manager: CLLocationManager, didFailWithError error: Error) {
        print("❌ Location error: \(error)")
    }
    
    func locationManager(_ manager: CLLocationManager, didChangeAuthorization status: CLAuthorizationStatus) {
        if status == .authorizedWhenInUse || status == .authorizedAlways {
            locationManager.requestLocation()
        }
    }
}

// MARK: - Utility Extensions and Helper Functions

extension RealTimePanchangService {

    // MARK: - Tamil Name Mappings

    private func getTamilTithiName(_ englishName: String) -> String {
        let tithiMap = [
            "Pratipada": "பிரதமை", "Dwitiya": "துவிதீயை", "Tritiya": "திருதீயை",
            "Chaturthi": "சதுர்த்தி", "Panchami": "பஞ்சமி", "Shashthi": "ஷஷ்டி",
            "Saptami": "சப்தமி", "Ashtami": "அஷ்டமி", "Navami": "நவமி",
            "Dashami": "தசமி", "Ekadashi": "ஏகாதசி", "Dwadashi": "துவாதசி",
            "Trayodashi": "திரயோதசி", "Chaturdashi": "சதுர்தசி", "Purnima": "பூர்ணிமை",
            "Amavasya": "அமாவாசை"
        ]
        return tithiMap[englishName] ?? englishName
    }

    private func getTamilNakshatraName(_ englishName: String) -> String {
        let nakshatraMap = [
            "Ashwini": "அஸ்வினி", "Bharani": "பரணி", "Krittika": "கிருத்திகை",
            "Rohini": "ரோஹிணி", "Mrigashira": "மிருகசீர்ஷம்", "Ardra": "ஆர்த்ரா",
            "Punarvasu": "புனர்வசு", "Pushya": "பூசம்", "Ashlesha": "ஆயில்யம்",
            "Magha": "மகம்", "Purva Phalguni": "பூர்வ பால்குனி", "Uttara Phalguni": "உத்தர பால்குனி",
            "Hasta": "ஹஸ்தம்", "Chitra": "சித்திரை", "Swati": "ஸ்வாதி",
            "Vishakha": "விசாகம்", "Anuradha": "அனுராதா", "Jyeshtha": "ஜ்யேஷ்டா",
            "Mula": "மூலம்", "Purva Ashadha": "பூர்வ ஆஷாடா", "Uttara Ashadha": "உத்தர ஆஷாடா",
            "Shravana": "ஸ்ரவணம்", "Dhanishtha": "தனிஷ்டா", "Shatabhisha": "சதாபிஷம்",
            "Purva Bhadrapada": "பூர்வ பத்ரபதா", "Uttara Bhadrapada": "உத்தர பத்ரபதா", "Revati": "ரேவதி"
        ]
        return nakshatraMap[englishName] ?? englishName
    }

    private func getTamilYogaName(_ englishName: String) -> String {
        let yogaMap = [
            "Vishkumbha": "விஷ்கும்பா", "Priti": "ப்ரீதி", "Ayushman": "ஆயுஷ்மான்",
            "Saubhagya": "சௌபாக்ய", "Shobhana": "சோபன", "Atiganda": "அதிகண்ட",
            "Sukarma": "சுகர்ம", "Dhriti": "த்ருதி", "Shula": "சூல",
            "Ganda": "கண்ட", "Vriddhi": "வ்ருத்தி", "Dhruva": "த்ருவ",
            "Vyaghata": "வ்யாகாத", "Harshana": "ஹர்ஷண", "Vajra": "வஜ்ர",
            "Siddhi": "சித்தி", "Vyatipata": "வ்யதீபாத", "Variyana": "வரீயான்",
            "Parigha": "பரிக", "Shiva": "சிவ", "Siddha": "சித்த",
            "Sadhya": "சாத்ய", "Shubha": "சுப", "Shukla": "சுக்ல",
            "Brahma": "பிரம்ம", "Indra": "இந்திர", "Vaidhriti": "வைத்ருதி"
        ]
        return yogaMap[englishName] ?? englishName
    }

    private func getTamilKaranaName(_ englishName: String) -> String {
        let karanaMap = [
            "Bava": "பவ", "Balava": "பாலவ", "Kaulava": "கௌலவ",
            "Taitila": "தைதில", "Gara": "கர", "Vanija": "வணிஜ",
            "Vishti": "விஷ்டி", "Sakuna": "சகுன", "Chatushpada": "சதுஷ்பாத",
            "Naga": "நாக", "Kimstughna": "கிம்ஸ்துக்ன"
        ]
        return karanaMap[englishName] ?? englishName
    }

    private func getTamilLunarMonthName(_ englishName: String) -> String {
        let monthMap = [
            "Chaitra": "சித்திரை", "Vaisakha": "வைகாசி", "Jyaistha": "ஆனி",
            "Ashadha": "ஆடி", "Shravana": "ஆவணி", "Bhadrapada": "புரட்டாசி",
            "Ashwin": "ஐப்பசி", "Kartika": "கார்த்திகை", "Agrahayana": "மார்கழி",
            "Pausha": "தை", "Magha": "மாசி", "Phalguna": "பங்குனி"
        ]
        return monthMap[englishName] ?? englishName
    }

    private func getTamilSeasonName(_ englishName: String) -> String {
        let seasonMap = [
            "Vasant (Spring)": "வசந்த காலம்",
            "Grishma (Summer)": "கோடை காலம்",
            "Varsha (Monsoon)": "மழை காலம்",
            "Sharad (Autumn)": "இலையுதிர் காலம்",
            "Shishir (Winter)": "குளிர் காலம்",
            "Hemant (Pre-winter)": "முன் குளிர் காலம்"
        ]
        return seasonMap[englishName] ?? englishName
    }

    // MARK: - Mapping Functions

    private func mapToTamilMonth(gregorianMonth: Int) -> PanchangTamilMonth {
        // Simplified mapping - in reality this would be more complex
        switch gregorianMonth {
        case 4: return .chithirai
        case 5: return .vaikasi
        case 6: return .aani
        case 7: return .aadi
        case 8: return .aavani
        case 9: return .purattasi
        case 10: return .aippasi
        case 11: return .karthigai
        case 12: return .margazhi
        case 1: return .thai
        case 2: return .maasi
        case 3: return .panguni
        default: return .chithirai
        }
    }

    private func mapToTamilSeason(month: Int) -> PanchangTamilSeason {
        switch month {
        case 3, 4, 5: return .spring
        case 6, 7, 8: return .summer
        case 9, 10, 11: return .monsoon
        case 12, 1, 2: return .winter
        default: return .spring
        }
    }

    // MARK: - Significance and Lord Functions

    private func getTithiLord(_ tithiNumber: Int) -> String? {
        let lords = [
            1: "Agni", 2: "Brahma", 3: "Gauri", 4: "Ganesha", 5: "Nagas",
            6: "Karttikeya", 7: "Surya", 8: "Shiva", 9: "Durga", 10: "Yama",
            11: "Vishnu", 12: "Vishnu", 13: "Kamadeva", 14: "Shiva", 15: "Chandra"
        ]
        return lords[tithiNumber % 15] ?? lords[tithiNumber]
    }

    private func getTithiSignificance(_ tithiNumber: Int) -> String? {
        let significance = [
            1: "New beginnings, starting projects",
            3: "Auspicious for all activities",
            5: "Learning, education",
            7: "Spiritual practices",
            10: "Completion of cycles",
            11: "Fasting, spiritual purification",
            13: "Love, relationships",
            15: "Full moon, completion"
        ]
        return significance[tithiNumber]
    }

    private func getNakshatraLord(_ nakshatraNumber: Int) -> String {
        let lords = [
            "Ketu", "Venus", "Sun", "Moon", "Mars", "Rahu", "Jupiter", "Saturn", "Mercury"
        ]
        return lords[(nakshatraNumber - 1) % 9]
    }

    private func getTamilNakshatraLord(_ nakshatraNumber: Int) -> String {
        let tamilLords = [
            "கேது", "வெள்ளி", "சூரியன்", "சந்திரன்", "செவ்வாய்", "ராகு", "குரு", "சனி", "புதன்"
        ]
        return tamilLords[(nakshatraNumber - 1) % 9]
    }

    private func getNakshatraSignificance(_ nakshatraNumber: Int) -> String? {
        // This would be a comprehensive mapping in a real implementation
        return "Nakshatra significance for \(nakshatraNumber)"
    }

    private func getYogaSignificance(_ yogaNumber: Int) -> String? {
        return "Yoga significance for \(yogaNumber)"
    }

    private func getSeasonDescription(_ seasonName: String) -> String? {
        let descriptions = [
            "Vasant (Spring)": "Pleasant weather, new growth",
            "Grishma (Summer)": "Hot weather, harvest time",
            "Varsha (Monsoon)": "Rainy season, fertility",
            "Sharad (Autumn)": "Post-monsoon, clear skies",
            "Shishir (Winter)": "Cold weather, introspection",
            "Hemant (Pre-winter)": "Cool weather, preparation"
        ]
        return descriptions[seasonName]
    }

    private func generateSignificance(tithi: Tithi, nakshatra: Nakshatra) -> String? {
        var significance = ""

        if tithi.isAuspicious {
            significance += "Auspicious tithi for new beginnings. "
        }

        if let tithiSig = tithi.significance {
            significance += tithiSig + " "
        }

        if let nakshaSig = nakshatra.significance {
            significance += nakshaSig
        }

        return significance.isEmpty ? nil : significance
    }

    // MARK: - Time Parsing and Calculation

    private func parseTimeString(_ timeString: String, date: Date? = nil) -> Date? {
        // ProKerala API returns ISO 8601 format: "2025-07-09T05:52:14+05:30"
        let isoFormatter = ISO8601DateFormatter()
        isoFormatter.formatOptions = [.withInternetDateTime, .withFractionalSeconds]

        if let parsedDate = isoFormatter.date(from: timeString) {
            return parsedDate
        }

        // Fallback to ISO format without fractional seconds
        isoFormatter.formatOptions = [.withInternetDateTime]
        if let parsedDate = isoFormatter.date(from: timeString) {
            return parsedDate
        }

        // Legacy fallback for old format
        let formatter = DateFormatter()
        formatter.dateFormat = "HH:mm:ss"

        if let time = formatter.date(from: timeString), let baseDate = date {
            let calendar = Calendar.current
            let timeComponents = calendar.dateComponents([.hour, .minute, .second], from: time)
            return calendar.date(bySettingHour: timeComponents.hour ?? 0,
                               minute: timeComponents.minute ?? 0,
                               second: timeComponents.second ?? 0,
                               of: baseDate)
        }

        return nil
    }

    private func calculateRahuKalam(weekday: Int, sunTimes: SunTimes) -> (start: Date, end: Date) {
        let dayLength = sunTimes.sunset.timeIntervalSince(sunTimes.sunrise)
        let segmentLength = dayLength / 8

        // Rahu Kalam timing varies by weekday (1 = Sunday, 2 = Monday, etc.)
        let rahuSegment: Int
        switch weekday {
        case 1: rahuSegment = 8 // Sunday: 8th segment (7:30-9:00 typically)
        case 2: rahuSegment = 2 // Monday: 2nd segment (7:30-9:00 typically)
        case 3: rahuSegment = 7 // Tuesday: 7th segment (3:00-4:30 typically)
        case 4: rahuSegment = 5 // Wednesday: 5th segment (12:00-1:30 typically)
        case 5: rahuSegment = 6 // Thursday: 6th segment (1:30-3:00 typically)
        case 6: rahuSegment = 4 // Friday: 4th segment (10:30-12:00 typically)
        case 7: rahuSegment = 3 // Saturday: 3rd segment (9:00-10:30 typically)
        default: rahuSegment = 8
        }

        let startTime = sunTimes.sunrise.addingTimeInterval(segmentLength * Double(rahuSegment - 1))
        let endTime = startTime.addingTimeInterval(segmentLength)

        return (start: startTime, end: endTime)
    }
}

// MARK: - Supabase Record Conversion

extension RealTimePanchangService {

    private func convertPanchangToSupabaseRecord(_ panchang: DailyPanchang) -> SupabasePanchangRecord {
        // Create weekday info from the current date
        let calendar = Calendar.current
        let weekdayNumber = calendar.component(.weekday, from: panchang.date)
        let weekdayInfo: [String: Any] = [
            "weekday_number": weekdayNumber,
            "weekday_name": DateFormatter().weekdaySymbols[weekdayNumber - 1],
            "vedic_weekday_number": weekdayNumber,
            "vedic_weekday_name": DateFormatter().weekdaySymbols[weekdayNumber - 1]
        ]

        // Create year info
        let year = calendar.component(.year, from: panchang.date)
        let yearInfo: [String: Any] = [
            "gregorian_year": year,
            "tamil_year": year + 1000, // Approximate Tamil year calculation
            "year_name": "Unknown"
        ]

        return SupabasePanchangRecord(
            id: panchang.id,
            date: panchang.date,
            locationInfo: try! JSONEncoder().encode(panchang.location).base64EncodedString(),
            tamilDate: try! JSONEncoder().encode(panchang.tamilDate).base64EncodedString(),
            sunTimes: try! JSONEncoder().encode(panchang.sunTimes).base64EncodedString(),
            moonTimes: panchang.moonTimes != nil ? try! JSONEncoder().encode(panchang.moonTimes!).base64EncodedString() : "",
            tithi: try! JSONEncoder().encode(panchang.tithi).base64EncodedString(),
            nakshatra: try! JSONEncoder().encode(panchang.nakshatra).base64EncodedString(),
            yoga: try! JSONEncoder().encode(panchang.yoga).base64EncodedString(),
            karana: try! JSONEncoder().encode(panchang.karana).base64EncodedString(),
            weekdayInfo: try! JSONSerialization.data(withJSONObject: weekdayInfo).base64EncodedString(),
            lunarMonth: try! JSONEncoder().encode(panchang.lunarMonth).base64EncodedString(),
            season: try! JSONEncoder().encode(panchang.season).base64EncodedString(),
            yearInfo: try! JSONSerialization.data(withJSONObject: yearInfo).base64EncodedString(),
            significance: panchang.significance,
            createdAt: panchang.createdAt,
            updatedAt: panchang.updatedAt
        )
    }

    // MARK: - Flexible Data Decoding

    /// Decodes data that could be either Base64 encoded (from iOS) or JSON string (from Python script)
    private func decodeFlexible<T: Codable>(_ type: T.Type, from stringData: String, using decoder: JSONDecoder) throws -> T {
        // First try to decode as Base64 (iOS format)
        if let base64Data = Data(base64Encoded: stringData) {
            do {
                return try decoder.decode(type, from: base64Data)
            } catch {
                print("⚠️ Failed to decode as Base64: \(error)")
            }
        }

        // If Base64 fails, try to decode as direct JSON string (Python format)
        if let jsonData = stringData.data(using: .utf8) {
            do {
                return try decoder.decode(type, from: jsonData)
            } catch {
                print("⚠️ Failed to decode as JSON string: \(error)")
                throw error
            }
        }

        throw NSError(domain: "DecodingError", code: 1, userInfo: [NSLocalizedDescriptionKey: "Unable to decode string data"])
    }

    private func convertSupabaseRecordToPanchang(_ record: SupabasePanchangRecord) -> DailyPanchang {
        let decoder = JSONDecoder()

        return DailyPanchang(
            id: record.id,
            date: record.date,
            location: try! decodeFlexible(LocationInfo.self, from: record.locationInfo, using: decoder),
            tamilDate: try! decodeFlexible(PanchangTamilDate.self, from: record.tamilDate, using: decoder),
            sunTimes: try! decodeFlexible(SunTimes.self, from: record.sunTimes, using: decoder),
            moonTimes: record.moonTimes.isEmpty ? nil : try! decodeFlexible(MoonTimes.self, from: record.moonTimes, using: decoder),
            tithi: try! decodeFlexible(Tithi.self, from: record.tithi, using: decoder),
            nakshatra: try! decodeFlexible(Nakshatra.self, from: record.nakshatra, using: decoder),
            yoga: try! decodeFlexible(Yoga.self, from: record.yoga, using: decoder),
            karana: try! decodeFlexible(Karana.self, from: record.karana, using: decoder),
            lunarMonth: try! decodeFlexible(LunarMonth.self, from: record.lunarMonth, using: decoder),
            season: try! decodeFlexible(Season.self, from: record.season, using: decoder),
            muhurat: [], // Will be populated separately
            inauspiciousTimes: [], // Will be populated separately
            festivals: [], // Will be populated separately
            significance: record.significance,
            createdAt: record.createdAt,
            updatedAt: record.updatedAt
        )
    }
}

// MARK: - Supabase Record Model

struct SupabasePanchangRecord: Codable {
    let id: UUID
    let date: Date
    let locationInfo: String  // Changed to String to handle both JSON and Base64
    let tamilDate: String
    let sunTimes: String
    let moonTimes: String
    let tithi: String
    let nakshatra: String
    let yoga: String
    let karana: String
    let weekdayInfo: String  // Added missing weekday_info column
    let lunarMonth: String
    let season: String
    let yearInfo: String     // Added missing year_info column
    let significance: String?
    let createdAt: Date
    let updatedAt: Date

    enum CodingKeys: String, CodingKey {
        case id, date, significance
        case locationInfo = "location_info"
        case tamilDate = "tamil_date"
        case sunTimes = "sun_times"
        case moonTimes = "moon_times"
        case tithi, nakshatra, yoga, karana
        case weekdayInfo = "weekday_info"
        case lunarMonth = "lunar_month"
        case season
        case yearInfo = "year_info"
        case createdAt = "created_at"
        case updatedAt = "updated_at"
    }

    // Memberwise initializer
    init(id: UUID, date: Date, locationInfo: String, tamilDate: String, sunTimes: String, moonTimes: String, tithi: String, nakshatra: String, yoga: String, karana: String, weekdayInfo: String, lunarMonth: String, season: String, yearInfo: String, significance: String?, createdAt: Date, updatedAt: Date) {
        self.id = id
        self.date = date
        self.locationInfo = locationInfo
        self.tamilDate = tamilDate
        self.sunTimes = sunTimes
        self.moonTimes = moonTimes
        self.tithi = tithi
        self.nakshatra = nakshatra
        self.yoga = yoga
        self.karana = karana
        self.weekdayInfo = weekdayInfo
        self.lunarMonth = lunarMonth
        self.season = season
        self.yearInfo = yearInfo
        self.significance = significance
        self.createdAt = createdAt
        self.updatedAt = updatedAt
    }

    // Custom decoder to handle Supabase date format
    init(from decoder: Decoder) throws {
        let container = try decoder.container(keyedBy: CodingKeys.self)

        id = try container.decode(UUID.self, forKey: .id)
        locationInfo = try container.decode(String.self, forKey: .locationInfo)
        tamilDate = try container.decode(String.self, forKey: .tamilDate)
        sunTimes = try container.decode(String.self, forKey: .sunTimes)
        moonTimes = try container.decode(String.self, forKey: .moonTimes)
        tithi = try container.decode(String.self, forKey: .tithi)
        nakshatra = try container.decode(String.self, forKey: .nakshatra)
        yoga = try container.decode(String.self, forKey: .yoga)
        karana = try container.decode(String.self, forKey: .karana)
        weekdayInfo = try container.decode(String.self, forKey: .weekdayInfo)
        lunarMonth = try container.decode(String.self, forKey: .lunarMonth)
        season = try container.decode(String.self, forKey: .season)
        yearInfo = try container.decode(String.self, forKey: .yearInfo)
        significance = try container.decodeIfPresent(String.self, forKey: .significance)

        // Handle date field - Supabase returns it as "YYYY-MM-DD" string
        if let dateString = try? container.decode(String.self, forKey: .date) {
            let formatter = DateFormatter()
            formatter.dateFormat = "yyyy-MM-dd"
            formatter.timeZone = TimeZone(identifier: "UTC")
            if let parsedDate = formatter.date(from: dateString) {
                date = parsedDate
            } else {
                throw DecodingError.dataCorrupted(DecodingError.Context(
                    codingPath: container.codingPath + [CodingKeys.date],
                    debugDescription: "Invalid date format: \(dateString)"
                ))
            }
        } else {
            date = try container.decode(Date.self, forKey: .date)
        }

        // Handle timestamp fields - Supabase returns these as ISO8601 strings
        let iso8601Formatter = ISO8601DateFormatter()

        if let createdAtString = try? container.decode(String.self, forKey: .createdAt) {
            createdAt = iso8601Formatter.date(from: createdAtString) ?? Date()
        } else {
            createdAt = try container.decode(Date.self, forKey: .createdAt)
        }

        if let updatedAtString = try? container.decode(String.self, forKey: .updatedAt) {
            updatedAt = iso8601Formatter.date(from: updatedAtString) ?? Date()
        } else {
            updatedAt = try container.decode(Date.self, forKey: .updatedAt)
        }
    }
}

// MARK: - Date Formatter Extension

extension DateFormatter {
    static let databaseDateFormatter: DateFormatter = {
        let formatter = DateFormatter()
        formatter.dateFormat = "yyyy-MM-dd"
        formatter.timeZone = TimeZone(identifier: "UTC")
        return formatter
    }()
}
