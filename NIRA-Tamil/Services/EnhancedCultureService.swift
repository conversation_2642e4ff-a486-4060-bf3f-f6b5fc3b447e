//
//  EnhancedCultureService.swift
//  NIRA-Tamil
//
//  Created by NIRA Team on 06/07/2025.
//

import Foundation
import Combine

// MARK: - Enhanced Culture Models

struct EnhancedCulturalCategory: Codable, Identifiable {
    let id: UUID
    let name: String
    let nameTamil: String
    let description: String?
    let emoji: String
    let colorTheme: String
    let categoryType: String
    let sortOrder: Int
    let isActive: Bool
    let contentCount: Int
    
    enum CodingKeys: String, CodingKey {
        case id, name, description, emoji, sortOrder
        case nameTamil = "name_tamil"
        case colorTheme = "color_theme"
        case categoryType = "category_type"
        case isActive = "is_active"
        case contentCount = "content_count"
    }
}

struct CinemaContent: Codable, Identifiable, Hashable {
    let id: UUID
    let title: String
    let titleTamil: String?
    let contentType: String
    let description: String
    let descriptionTamil: String?
    let culturalImpact: String?
    let historicalSignificance: String?
    let modernRelevance: String?
    let yearReleased: Int?
    let director: String?
    let directorTamil: String?
    let castMembers: [String]?
    let genre: String?
    let awards: [String]?
    let boxOfficeInfo: String?
    let culturalThemes: [String]?
    let audioUrl: String?
    let imageUrl: String?
    let trailerUrl: String?
    let romanization: String?
    let tags: [String]?
    let difficultyLevel: String
    let isFeatured: Bool
    
    enum CodingKeys: String, CodingKey {
        case id, title, description, genre, tags
        case titleTamil = "title_tamil"
        case contentType = "content_type"
        case descriptionTamil = "description_tamil"
        case culturalImpact = "cultural_impact"
        case historicalSignificance = "historical_significance"
        case modernRelevance = "modern_relevance"
        case yearReleased = "year_released"
        case director
        case directorTamil = "director_tamil"
        case castMembers = "cast_members"
        case awards
        case boxOfficeInfo = "box_office_info"
        case culturalThemes = "cultural_themes"
        case audioUrl = "audio_url"
        case imageUrl = "image_url"
        case trailerUrl = "trailer_url"
        case romanization
        case difficultyLevel = "difficulty_level"
        case isFeatured = "is_featured"
    }
}

struct SportsContent: Codable, Identifiable, Hashable {
    let id: UUID
    let name: String
    let nameTamil: String
    let sportType: String
    let description: String
    let descriptionTamil: String?
    let culturalSignificance: String?
    let historicalBackground: String?
    let rulesOverview: String?
    let modernStatus: String?
    let famousPersonalities: [String]?
    let majorEvents: [String]?
    let regionalVariations: String?
    let equipmentNeeded: [String]?
    let audioUrl: String?
    let imageUrl: String?
    let videoUrl: String?
    let romanization: String?
    let tags: [String]?
    let difficultyLevel: String
    let isFeatured: Bool

    enum CodingKeys: String, CodingKey {
        case id, name, description, tags
        case nameTamil = "name_tamil"
        case sportType = "sport_type"
        case descriptionTamil = "description_tamil"
        case culturalSignificance = "cultural_significance"
        case historicalBackground = "historical_background"
        case rulesOverview = "rules_overview"
        case modernStatus = "modern_status"
        case famousPersonalities = "famous_personalities"
        case majorEvents = "major_events"
        case regionalVariations = "regional_variations"
        case equipmentNeeded = "equipment_needed"
        case audioUrl = "audio_url"
        case imageUrl = "image_url"
        case videoUrl = "video_url"
        case romanization
        case difficultyLevel = "difficulty_level"
        case isFeatured = "is_featured"
    }
}

struct ArtsDanceContent: Codable, Identifiable, Hashable {
    let id: UUID
    let title: String
    let titleTamil: String?
    let contentType: String
    let description: String
    let descriptionTamil: String?
    let culturalImpact: String?
    let historicalSignificance: String?
    let modernRelevance: String?
    let artFormType: String?
    let originRegion: String?
    let keyPractitioners: [String]?
    let techniques: [String]?
    let instrumentsUsed: [String]?
    let costumeDetails: String?
    let performanceContext: String?
    let learningResources: [String]?
    let audioUrl: String?
    let imageUrl: String?
    let videoUrl: String?
    let romanization: String?
    let tags: [String]?
    let difficultyLevel: String
    let isFeatured: Bool

    enum CodingKeys: String, CodingKey {
        case id, title, description, tags
        case titleTamil = "title_tamil"
        case contentType = "content_type"
        case descriptionTamil = "description_tamil"
        case culturalImpact = "cultural_impact"
        case historicalSignificance = "historical_significance"
        case modernRelevance = "modern_relevance"
        case artFormType = "art_form_type"
        case originRegion = "origin_region"
        case keyPractitioners = "key_practitioners"
        case techniques
        case instrumentsUsed = "instruments_used"
        case costumeDetails = "costume_details"
        case performanceContext = "performance_context"
        case learningResources = "learning_resources"
        case audioUrl = "audio_url"
        case imageUrl = "image_url"
        case videoUrl = "video_url"
        case romanization
        case difficultyLevel = "difficulty_level"
        case isFeatured = "is_featured"
    }
}

struct MusicContent: Codable, Identifiable, Hashable {
    let id: UUID
    let title: String
    let titleTamil: String?
    let contentType: String
    let description: String
    let descriptionTamil: String?
    let culturalImpact: String?
    let historicalSignificance: String?
    let modernRelevance: String?
    let musicType: String?
    let ragaTalaInfo: String?
    let composerArtist: String?
    let composerArtistTamil: String?
    let instrumentsFeatured: [String]?
    let lyricalThemes: [String]?
    let performanceContext: String?
    let learningProgression: String?
    let notableRenditions: [String]?
    let audioUrl: String?
    let imageUrl: String?
    let performanceVideoUrl: String?
    let romanization: String?
    let tags: [String]?
    let difficultyLevel: String
    let isFeatured: Bool

    enum CodingKeys: String, CodingKey {
        case id, title, description, tags
        case titleTamil = "title_tamil"
        case contentType = "content_type"
        case descriptionTamil = "description_tamil"
        case culturalImpact = "cultural_impact"
        case historicalSignificance = "historical_significance"
        case modernRelevance = "modern_relevance"
        case musicType = "music_type"
        case ragaTalaInfo = "raga_tala_info"
        case composerArtist = "composer_artist"
        case composerArtistTamil = "composer_artist_tamil"
        case instrumentsFeatured = "instruments_featured"
        case lyricalThemes = "lyrical_themes"
        case performanceContext = "performance_context"
        case learningProgression = "learning_progression"
        case notableRenditions = "notable_renditions"
        case audioUrl = "audio_url"
        case imageUrl = "image_url"
        case performanceVideoUrl = "performance_video_url"
        case romanization
        case difficultyLevel = "difficulty_level"
        case isFeatured = "is_featured"
    }
}

struct FestivalsContent: Codable, Identifiable, Hashable {
    let id: UUID
    let title: String
    let titleTamil: String?
    let contentType: String
    let description: String
    let descriptionTamil: String?
    let culturalImpact: String?
    let historicalSignificance: String?
    let modernRelevance: String?
    let festivalType: String?
    let celebrationPeriod: String?
    let regionalVariations: [String]?
    let traditionalFoods: [String]?
    let ritualsPractices: [String]?
    let decorationsSymbols: [String]?
    let songsMusic: [String]?
    let modernCelebrations: String?
    let audioUrl: String?
    let imageUrl: String?
    let videoUrl: String?
    let romanization: String?
    let tags: [String]?
    let difficultyLevel: String
    let isFeatured: Bool

    enum CodingKeys: String, CodingKey {
        case id, title, description, tags
        case titleTamil = "title_tamil"
        case contentType = "content_type"
        case descriptionTamil = "description_tamil"
        case culturalImpact = "cultural_impact"
        case historicalSignificance = "historical_significance"
        case modernRelevance = "modern_relevance"
        case festivalType = "festival_type"
        case celebrationPeriod = "celebration_period"
        case regionalVariations = "regional_variations"
        case traditionalFoods = "traditional_foods"
        case ritualsPractices = "rituals_practices"
        case decorationsSymbols = "decorations_symbols"
        case songsMusic = "songs_music"
        case modernCelebrations = "modern_celebrations"
        case audioUrl = "audio_url"
        case imageUrl = "image_url"
        case videoUrl = "video_url"
        case romanization
        case difficultyLevel = "difficulty_level"
        case isFeatured = "is_featured"
    }
}

struct ArchitectureContent: Codable, Identifiable, Hashable {
    let id: UUID
    let title: String
    let titleTamil: String?
    let contentType: String
    let description: String
    let descriptionTamil: String?
    let culturalImpact: String?
    let historicalSignificance: String?
    let modernRelevance: String?
    let architectureType: String?
    let constructionPeriod: String?
    let architecturalStyle: String?
    let location: String?
    let keyFeatures: [String]?
    let materialsUsed: [String]?
    let architecturalElements: [String]?
    let culturalSymbolism: String?
    let preservationStatus: String?
    let audioUrl: String?
    let imageUrl: String?
    let virtualTourUrl: String?
    let romanization: String?
    let tags: [String]?
    let difficultyLevel: String
    let isFeatured: Bool

    enum CodingKeys: String, CodingKey {
        case id, title, description, tags, location
        case titleTamil = "title_tamil"
        case contentType = "content_type"
        case descriptionTamil = "description_tamil"
        case culturalImpact = "cultural_impact"
        case historicalSignificance = "historical_significance"
        case modernRelevance = "modern_relevance"
        case architectureType = "architecture_type"
        case constructionPeriod = "construction_period"
        case architecturalStyle = "architectural_style"
        case keyFeatures = "key_features"
        case materialsUsed = "materials_used"
        case architecturalElements = "architectural_elements"
        case culturalSymbolism = "cultural_symbolism"
        case preservationStatus = "preservation_status"
        case audioUrl = "audio_url"
        case imageUrl = "image_url"
        case virtualTourUrl = "virtual_tour_url"
        case romanization
        case difficultyLevel = "difficulty_level"
        case isFeatured = "is_featured"
    }
}

struct CuisineContent: Codable, Identifiable, Hashable {
    let id: UUID
    let title: String
    let titleTamil: String?
    let contentType: String
    let description: String
    let descriptionTamil: String?
    let culturalImpact: String?
    let historicalSignificance: String?
    let modernRelevance: String?
    let cuisineType: String?
    let preparationMethod: String?
    let keyIngredients: [String]?
    let regionalVariations: [String]?
    let nutritionalBenefits: String?
    let culturalOccasions: [String]?
    let cookingTechniques: [String]?
    let servingTraditions: String?
    let modernAdaptations: String?
    let audioUrl: String?
    let imageUrl: String?
    let recipeVideoUrl: String?
    let romanization: String?
    let tags: [String]?
    let difficultyLevel: String
    let isFeatured: Bool

    enum CodingKeys: String, CodingKey {
        case id, title, description, tags
        case titleTamil = "title_tamil"
        case contentType = "content_type"
        case descriptionTamil = "description_tamil"
        case culturalImpact = "cultural_impact"
        case historicalSignificance = "historical_significance"
        case modernRelevance = "modern_relevance"
        case cuisineType = "cuisine_type"
        case preparationMethod = "preparation_method"
        case keyIngredients = "key_ingredients"
        case regionalVariations = "regional_variations"
        case nutritionalBenefits = "nutritional_benefits"
        case culturalOccasions = "cultural_occasions"
        case cookingTechniques = "cooking_techniques"
        case servingTraditions = "serving_traditions"
        case modernAdaptations = "modern_adaptations"
        case audioUrl = "audio_url"
        case imageUrl = "image_url"
        case recipeVideoUrl = "recipe_video_url"
        case romanization
        case difficultyLevel = "difficulty_level"
        case isFeatured = "is_featured"
    }
}

// MARK: - Enhanced Culture Service

@MainActor
class EnhancedCultureService: ObservableObject {
    static let shared = EnhancedCultureService()

    @Published var categories: [EnhancedCulturalCategory] = []
    @Published var allCinemaContent: [CinemaContent] = []
    @Published var allSportsContent: [SportsContent] = []
    @Published var allArtsDanceContent: [ArtsDanceContent] = []
    @Published var allMusicContent: [MusicContent] = []
    @Published var allFestivalsContent: [FestivalsContent] = []
    @Published var allArchitectureContent: [ArchitectureContent] = []
    @Published var allCuisineContent: [CuisineContent] = []

    @Published var featuredCinemaContent: [CinemaContent] = []
    @Published var featuredSportsContent: [SportsContent] = []
    @Published var featuredArtsDanceContent: [ArtsDanceContent] = []
    @Published var featuredMusicContent: [MusicContent] = []
    @Published var featuredFestivalsContent: [FestivalsContent] = []
    @Published var featuredArchitectureContent: [ArchitectureContent] = []
    @Published var featuredCuisineContent: [CuisineContent] = []

    @Published var isLoading = false
    @Published var error: Error?

    private let supabaseClient = NIRASupabaseClient.shared
    private var cancellables = Set<AnyCancellable>()

    private init() {
        // Initialize with empty data, will load from Supabase
    }
    
    // MARK: - Public Methods
    
    func loadContent() async {
        isLoading = true
        error = nil

        // Load categories
        await loadCategories()

        // Load all content types
        await loadCinemaContent()
        await loadSportsContent()
        await loadArtsDanceContent()
        await loadMusicContent()
        await loadFestivalsContent()
        await loadArchitectureContent()
        await loadCuisineContent()

        // Set featured content
        updateFeaturedContent()

        // Update categories with actual content counts
        await MainActor.run {
            updateCategoryContentCounts()
        }

        isLoading = false
    }

    private func updateCategoryContentCounts() {
        for i in 0..<categories.count {
            let category = categories[i]
            let content = getContent(for: category.name)
            categories[i] = EnhancedCulturalCategory(
                id: category.id,
                name: category.name,
                nameTamil: category.nameTamil,
                description: category.description,
                emoji: category.emoji,
                colorTheme: category.colorTheme,
                categoryType: category.categoryType,
                sortOrder: category.sortOrder,
                isActive: category.isActive,
                contentCount: content.count
            )
        }
        print("📊 Updated category content counts: \(categories.map { "\($0.name): \($0.contentCount)" }.joined(separator: ", "))")
    }
    
    func getFeaturedContent(for categoryType: String) -> [Any] {
        switch categoryType {
        case "cinema":
            return featuredCinemaContent
        case "sports":
            return featuredSportsContent
        case "arts_dance":
            return featuredArtsDanceContent
        case "music":
            return featuredMusicContent
        case "festivals":
            return featuredFestivalsContent
        case "architecture":
            return featuredArchitectureContent
        case "cuisine":
            return featuredCuisineContent
        default:
            return []
        }
    }

    func getContent(for categoryType: String) -> [Any] {
        let normalizedType = categoryType.lowercased()
            .replacingOccurrences(of: " ", with: "_")
            .replacingOccurrences(of: "&", with: "")

        print("🔍 Getting content for category: '\(categoryType)' -> normalized: '\(normalizedType)'")

        switch normalizedType {
        case "cinema":
            print("📽️ Returning \(allCinemaContent.count) cinema items")
            return allCinemaContent
        case "sports":
            print("🏆 Returning \(allSportsContent.count) sports items")
            return allSportsContent
        case "arts_dance", "artsdance":
            print("💃 Returning \(allArtsDanceContent.count) arts dance items")
            return allArtsDanceContent
        case "music":
            print("🎵 Returning \(allMusicContent.count) music items")
            return allMusicContent
        case "festivals":
            print("🎊 Returning \(allFestivalsContent.count) festivals items")
            return allFestivalsContent
        case "architecture":
            print("🏛️ Returning \(allArchitectureContent.count) architecture items")
            return allArchitectureContent
        case "cuisine":
            print("🍛 Returning \(allCuisineContent.count) cuisine items")
            return allCuisineContent
        default:
            print("❌ No content found for category: '\(normalizedType)'")
            return []
        }
    }

    // MARK: - Private Methods
    
    private func loadCategories() async {
        do {
            let _ = try await supabaseClient.client.from("cultural_categories")
                .select("*, content_count:cultural_insights(count)")
                .eq("is_active", value: true)
                .order("sort_order")
                .execute()

            // For now, use mock data until Supabase integration is complete
            categories = createMockCategories()

        } catch {
            print("❌ Error loading cultural categories: \(error)")
            // Fallback to mock data
            categories = createMockCategories()
        }
    }
    
    private func loadCinemaContent() async {
        do {
            let response: [CinemaContent] = try await supabaseClient.client.from("cinema_content")
                .select("*")
                .order("created_at", ascending: false)
                .execute()
                .value

            allCinemaContent = response

        } catch {
            print("❌ Error loading cinema content: \(error)")
            // Fallback to mock data
            allCinemaContent = createMockCinemaContent()
        }
    }
    
    private func loadSportsContent() async {
        do {
            let response: [SportsContent] = try await supabaseClient.client.from("sports_content")
                .select("*")
                .order("created_at", ascending: false)
                .execute()
                .value

            allSportsContent = response

        } catch {
            print("❌ Error loading sports content: \(error)")
            // Fallback to mock data
            allSportsContent = createMockSportsContent()
        }
    }

    private func loadArtsDanceContent() async {
        do {
            let response: [ArtsDanceContent] = try await supabaseClient.client.from("arts_dance_content")
                .select("*")
                .order("created_at", ascending: false)
                .execute()
                .value

            allArtsDanceContent = response
            print("✅ Loaded \(response.count) arts dance content items")

        } catch {
            print("❌ Error loading arts dance content: \(error)")
            allArtsDanceContent = []
        }
    }

    private func loadMusicContent() async {
        do {
            let response: [MusicContent] = try await supabaseClient.client.from("music_content")
                .select("*")
                .order("created_at", ascending: false)
                .execute()
                .value

            allMusicContent = response
            print("✅ Loaded \(response.count) music content items")

        } catch {
            print("❌ Error loading music content: \(error)")
            allMusicContent = []
        }
    }

    private func loadFestivalsContent() async {
        do {
            let response: [FestivalsContent] = try await supabaseClient.client.from("festivals_content")
                .select("*")
                .order("created_at", ascending: false)
                .execute()
                .value

            allFestivalsContent = response

        } catch {
            print("❌ Error loading festivals content: \(error)")
            allFestivalsContent = []
        }
    }

    private func loadArchitectureContent() async {
        do {
            let response: [ArchitectureContent] = try await supabaseClient.client.from("architecture_content")
                .select("*")
                .order("created_at", ascending: false)
                .execute()
                .value

            allArchitectureContent = response

        } catch {
            print("❌ Error loading architecture content: \(error)")
            allArchitectureContent = []
        }
    }

    private func loadCuisineContent() async {
        do {
            let response: [CuisineContent] = try await supabaseClient.client.from("cuisine_content")
                .select("*")
                .order("created_at", ascending: false)
                .execute()
                .value

            allCuisineContent = response

        } catch {
            print("❌ Error loading cuisine content: \(error)")
            allCuisineContent = []
        }
    }
    
    private func updateFeaturedContent() {
        featuredCinemaContent = Array(allCinemaContent.filter { $0.isFeatured }.prefix(3))
        featuredSportsContent = Array(allSportsContent.filter { $0.isFeatured }.prefix(3))
        featuredArtsDanceContent = Array(allArtsDanceContent.filter { $0.isFeatured }.prefix(3))
        featuredMusicContent = Array(allMusicContent.filter { $0.isFeatured }.prefix(3))
        featuredFestivalsContent = Array(allFestivalsContent.filter { $0.isFeatured }.prefix(3))
        featuredArchitectureContent = Array(allArchitectureContent.filter { $0.isFeatured }.prefix(3))
        featuredCuisineContent = Array(allCuisineContent.filter { $0.isFeatured }.prefix(3))
    }
    
    // MARK: - Mock Data (temporary until Supabase integration)
    
    private func createMockCategories() -> [EnhancedCulturalCategory] {
        return [
            EnhancedCulturalCategory(id: UUID(), name: "Arts & Dance", nameTamil: "கலை மற்றும் நடனம்", description: "Traditional and classical Tamil arts and dance forms", emoji: "💃", colorTheme: "pink", categoryType: "arts_dance", sortOrder: 1, isActive: true, contentCount: 15),
            EnhancedCulturalCategory(id: UUID(), name: "Music", nameTamil: "இசை", description: "Carnatic music and Tamil musical traditions", emoji: "🎵", colorTheme: "purple", categoryType: "music", sortOrder: 2, isActive: true, contentCount: 25),
            EnhancedCulturalCategory(id: UUID(), name: "Festivals", nameTamil: "திருவிழாக்கள்", description: "Tamil festivals and celebrations", emoji: "🎊", colorTheme: "orange", categoryType: "festivals", sortOrder: 3, isActive: true, contentCount: 15),
            EnhancedCulturalCategory(id: UUID(), name: "Architecture", nameTamil: "கட்டிடக்கலை", description: "Tamil architectural styles and monuments", emoji: "🏛️", colorTheme: "brown", categoryType: "architecture", sortOrder: 4, isActive: true, contentCount: 15),
            EnhancedCulturalCategory(id: UUID(), name: "Cuisine", nameTamil: "உணவு", description: "Traditional Tamil food and culinary arts", emoji: "🍛", colorTheme: "green", categoryType: "cuisine", sortOrder: 5, isActive: true, contentCount: 15),
            EnhancedCulturalCategory(id: UUID(), name: "Cinema", nameTamil: "சினிமா", description: "Tamil film industry and cultural impact", emoji: "🎬", colorTheme: "red", categoryType: "cinema", sortOrder: 6, isActive: true, contentCount: 25),
            EnhancedCulturalCategory(id: UUID(), name: "Sports", nameTamil: "விளையாட்டு", description: "Traditional and modern Tamil sports", emoji: "🏆", colorTheme: "blue", categoryType: "sports", sortOrder: 7, isActive: true, contentCount: 15)
        ]
    }
    
    private func createMockCinemaContent() -> [CinemaContent] {
        return [
            CinemaContent(id: UUID(), title: "History of Tamil Cinema", titleTamil: "தமிழ் சினிமா வரலாறு", contentType: "history", description: "Tamil cinema, also known as Kollywood, has a rich history spanning over a century, from silent films to modern digital cinema.", descriptionTamil: "தமிழ் சினிமா, கோலிவுட் என்றும் அழைக்கப்படும், ஒரு நூற்றாண்டுக்கும் மேலான வளமான வரலாற்றைக் கொண்டுள்ளது.", culturalImpact: "Tamil cinema has significantly influenced Tamil culture, language, politics, and social movements", historicalSignificance: nil, modernRelevance: nil, yearReleased: 1931, director: "Various", directorTamil: "பல்வேறு இயக்குநர்கள்", castMembers: [], genre: "Documentary", awards: [], boxOfficeInfo: nil, culturalThemes: ["Cultural identity", "Social reform", "Political awareness", "Language preservation"], audioUrl: nil, imageUrl: nil, trailerUrl: nil, romanization: nil, tags: ["history", "culture", "entertainment", "society"], difficultyLevel: "intermediate", isFeatured: true),
            CinemaContent(id: UUID(), title: "M.G. Ramachandran", titleTamil: "எம்.ஜி.ஆர்", contentType: "personality", description: "Legendary actor and politician who became the Chief Minister of Tamil Nadu, known for his philanthropic roles.", descriptionTamil: "தமிழ்நாட்டின் முதலமைச்சராக ஆன புகழ்பெற்ற நடிகர் மற்றும் அரசியல்வாதி.", culturalImpact: "Transformed Tamil cinema and politics, created the template for actor-politicians", historicalSignificance: nil, modernRelevance: nil, yearReleased: 1950, director: nil, directorTamil: nil, castMembers: [], genre: "Biography", awards: [], boxOfficeInfo: nil, culturalThemes: ["Social justice", "Philanthropy", "Political leadership", "Cultural icon"], audioUrl: nil, imageUrl: nil, trailerUrl: nil, romanization: nil, tags: ["actor", "politician", "social_reform", "leadership"], difficultyLevel: "beginner", isFeatured: true)
        ]
    }
    
    private func createMockSportsContent() -> [SportsContent] {
        return [
            SportsContent(id: UUID(), name: "Jallikattu", nameTamil: "ஜல்லிக்கட்டு", sportType: "traditional", description: "Traditional bull-taming sport practiced during Pongal festival, symbolizing courage and agricultural heritage.", descriptionTamil: "பொங்கல் திருவிழாவின் போது நடைபெறும் பாரம்பரிய காளை அடக்கும் விளையாட்டு.", culturalSignificance: "Represents Tamil agricultural culture, courage, and the relationship between humans and cattle", historicalBackground: "Ancient sport mentioned in Tamil literature, practiced for over 2000 years", rulesOverview: "Participants attempt to hold onto a bull for a specific duration or distance without using ropes or weapons", modernStatus: "Continues to be practiced with modern safety regulations and government support", famousPersonalities: [], majorEvents: [], regionalVariations: nil, equipmentNeeded: [], audioUrl: nil, imageUrl: nil, videoUrl: nil, romanization: "Jallikattu", tags: ["traditional", "festival", "courage", "agriculture"], difficultyLevel: "intermediate", isFeatured: true),
            SportsContent(id: UUID(), name: "Kabaddi", nameTamil: "கபடி", sportType: "traditional", description: "Contact team sport where players attempt to tag opponents while holding their breath.", descriptionTamil: "மூச்சை அடக்கி எதிரணி வீரர்களைத் தொட்டு வரும் தொடர்பு விளையாட்டு.", culturalSignificance: "Develops physical fitness, mental strength, and team coordination", historicalBackground: "Ancient sport with roots in Tamil Nadu, now played internationally", rulesOverview: "Two teams take turns sending raiders to tag opponents and return to their half", modernStatus: "Professional leagues and international competitions, including Asian Games", famousPersonalities: [], majorEvents: [], regionalVariations: nil, equipmentNeeded: [], audioUrl: nil, imageUrl: nil, videoUrl: nil, romanization: "Kabaddi", tags: ["traditional", "team_sport", "fitness", "strategy"], difficultyLevel: "beginner", isFeatured: true)
        ]
    }
}
