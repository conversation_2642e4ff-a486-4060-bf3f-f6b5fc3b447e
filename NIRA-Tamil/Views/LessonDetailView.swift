//
//  LessonDetailView.swift
//  NIRA-Tamil
//
//  Created by NIRA Team on 19/06/2025.
//

import SwiftUI

struct LessonDetailView: View {
    let lesson: SupabaseLesson
    @Environment(\.dismiss) private var dismiss
    @StateObject private var supabaseContentService = SupabaseContentService.shared
    @StateObject private var audioManager = AudioContentManager.shared
    
    // Content state
    @State private var vocabulary: [TamilSupabaseVocabulary] = []
    @State private var conversations: [TamilSupabaseConversation] = []
    @State private var grammarTopics: [TamilSupabaseGrammarTopic] = []
    @State private var practiceExercises: [TamilSupabasePracticeExercise] = []
    
    // UI state
    @State private var isLoading = true
    @State private var loadingError: String?
    @State private var isVocabularyExpanded = false
    @State private var isConversationsExpanded = false
    @State private var isGrammarExpanded = false
    @State private var isPracticeExpanded = false
    
    var body: some View {
        NavigationView {
            ZStack {
                // Background gradient
                LinearGradient(
                    gradient: Gradient(colors: [
                        Color(red: 0.95, green: 0.97, blue: 1.0),
                        Color(red: 0.88, green: 0.92, blue: 0.98),
                        Color(red: 0.82, green: 0.88, blue: 0.96)
                    ]),
                    startPoint: .topLeading,
                    endPoint: .bottomTrailing
                )
                .ignoresSafeArea()
                
                if isLoading {
                    loadingView
                } else if let error = loadingError {
                    errorView(error)
                } else {
                    contentView
                }
            }
            .navigationTitle(lesson.title)
            .navigationBarTitleDisplayMode(.large)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("Done") {
                        dismiss()
                    }
                }
            }
        }
        .task {
            await loadLessonContent()
        }
    }
    
    private var loadingView: some View {
        VStack(spacing: 20) {
            ProgressView()
                .scaleEffect(1.5)
            Text("Loading lesson content...")
                .font(.headline)
                .foregroundColor(.secondary)
        }
    }
    
    private func errorView(_ error: String) -> some View {
        VStack(spacing: 20) {
            Image(systemName: "exclamationmark.triangle")
                .font(.system(size: 50))
                .foregroundColor(.orange)
            
            Text("Error Loading Content")
                .font(.headline)
            
            Text(error)
                .font(.body)
                .foregroundColor(.secondary)
                .multilineTextAlignment(.center)
                .padding(.horizontal)
            
            Button("Retry") {
                Task { @MainActor in
                    await loadLessonContent()
                }
            }
            .buttonStyle(.borderedProminent)
        }
        .padding()
    }
    
    private var contentView: some View {
        ScrollView {
            VStack(spacing: 24) {
                // Header
                lessonHeaderView
                
                // Content sections
                if !vocabulary.isEmpty {
                    vocabularySection
                }
                
                if !conversations.isEmpty {
                    conversationsSection
                }
                
                if !grammarTopics.isEmpty {
                    grammarSection
                }
                
                if !practiceExercises.isEmpty {
                    practiceSection
                }
            }
            .padding()
        }
    }
    
    private var lessonHeaderView: some View {
        VStack(alignment: .leading, spacing: 16) {
            HStack {
                VStack(alignment: .leading, spacing: 8) {
                    Text(lesson.title)
                        .font(.largeTitle)
                        .fontWeight(.bold)
                        .foregroundColor(.primary)
                    
                    if let description = lesson.description {
                        Text(description)
                            .font(.body)
                            .foregroundColor(.secondary)
                    }
                }
                
                Spacer()
                
                Text(lesson.difficultyText)
                    .font(.caption)
                    .fontWeight(.semibold)
                    .foregroundColor(.white)
                    .padding(.horizontal, 12)
                    .padding(.vertical, 6)
                    .background(lesson.difficultyColor)
                    .cornerRadius(12)
            }
            
            // Metadata
            HStack {
                Label(lesson.formattedDuration, systemImage: "clock")
                    .font(.caption)
                    .foregroundColor(.secondary)
                
                if lesson.hasAudio == true {
                    Label("Audio", systemImage: "speaker.wave.2")
                        .font(.caption)
                        .foregroundColor(.niraPrimary)
                }
                
                Spacer()
                
                Text(lesson.lessonType.capitalized)
                    .font(.caption)
                    .fontWeight(.medium)
                    .foregroundColor(.niraPrimary)
                    .padding(.horizontal, 8)
                    .padding(.vertical, 4)
                    .background(Color.niraPrimary.opacity(0.1))
                    .cornerRadius(8)
            }
        }
        .padding(20)
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(.ultraThinMaterial)
                .shadow(color: .black.opacity(0.1), radius: 8, x: 0, y: 4)
        )
    }
    
    private var vocabularySection: some View {
        DisclosureGroup(
            isExpanded: $isVocabularyExpanded,
            content: {
                LazyVStack(spacing: 12) {
                    ForEach(vocabulary, id: \.id) { vocab in
                        SupabaseVocabularyItemView(vocabulary: vocab)
                    }
                }
                .padding(.top, 12)
            },
            label: {
                HStack {
                    Image(systemName: "book.fill")
                        .foregroundColor(.blue)
                    Text("Vocabulary (\(vocabulary.count))")
                        .font(.headline)
                        .fontWeight(.semibold)
                    Spacer()
                }
            }
        )
        .padding(16)
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(.ultraThinMaterial)
        )
    }
    
    private var conversationsSection: some View {
        DisclosureGroup(
            isExpanded: $isConversationsExpanded,
            content: {
                LazyVStack(spacing: 12) {
                    ForEach(conversations, id: \.id) { conversation in
                        SupabaseConversationItemView(conversation: conversation)
                    }
                }
                .padding(.top, 12)
            },
            label: {
                HStack {
                    Image(systemName: "bubble.left.and.bubble.right.fill")
                        .foregroundColor(.green)
                    Text("Conversations (\(conversations.count))")
                        .font(.headline)
                        .fontWeight(.semibold)
                    Spacer()
                }
            }
        )
        .padding(16)
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(.ultraThinMaterial)
        )
    }
    
    private var grammarSection: some View {
        DisclosureGroup(
            isExpanded: $isGrammarExpanded,
            content: {
                LazyVStack(spacing: 12) {
                    ForEach(grammarTopics, id: \.id) { grammar in
                        SupabaseGrammarItemView(grammar: grammar)
                    }
                }
                .padding(.top, 12)
            },
            label: {
                HStack {
                    Image(systemName: "textformat")
                        .foregroundColor(.orange)
                    Text("Grammar (\(grammarTopics.count))")
                        .font(.headline)
                        .fontWeight(.semibold)
                    Spacer()
                }
            }
        )
        .padding(16)
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(.ultraThinMaterial)
        )
    }
    
    private var practiceSection: some View {
        DisclosureGroup(
            isExpanded: $isPracticeExpanded,
            content: {
                LazyVStack(spacing: 12) {
                    ForEach(practiceExercises, id: \.id) { exercise in
                        SupabasePracticeItemView(exercise: exercise)
                    }
                }
                .padding(.top, 12)
            },
            label: {
                HStack {
                    Image(systemName: "pencil.and.outline")
                        .foregroundColor(.purple)
                    Text("Practice (\(practiceExercises.count))")
                        .font(.headline)
                        .fontWeight(.semibold)
                    Spacer()
                }
            }
        )
        .padding(16)
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(.ultraThinMaterial)
        )
    }
    
    @MainActor
    private func loadLessonContent() async {
        isLoading = true
        loadingError = nil
        
        // Load content from Supabase using the lesson ID
        async let vocabTask = supabaseContentService.fetchVocabulary(for: lesson.id.uuidString)
        async let conversationsTask = supabaseContentService.fetchConversations(for: lesson.id.uuidString)
        async let grammarTask = supabaseContentService.fetchGrammarTopics(for: lesson.id.uuidString)
        async let practiceTask = supabaseContentService.fetchPracticeExercises(for: lesson.id.uuidString)

        let (vocabResult, conversationsResult, grammarResult, practiceResult) = await (
            vocabTask, conversationsTask, grammarTask, practiceTask
        )

        vocabulary = vocabResult
        conversations = conversationsResult
        grammarTopics = grammarResult
        practiceExercises = practiceResult

        print("✅ Loaded lesson content: \(vocabulary.count) vocab, \(conversations.count) conversations, \(grammarTopics.count) grammar, \(practiceExercises.count) practice")
        
        isLoading = false
    }
}

// MARK: - Content Item Views

struct SupabaseVocabularyItemView: View {
    let vocabulary: TamilSupabaseVocabulary
    @State private var isExpanded = false
    
    var body: some View {
        VStack(alignment: .leading, spacing: 8) {
            HStack {
                VStack(alignment: .leading, spacing: 4) {
                    Text(vocabulary.englishWord)
                        .font(.headline)
                        .fontWeight(.semibold)
                    
                    Text(vocabulary.tamilTranslation)
                        .font(.title2)
                        .foregroundColor(.niraPrimary)
                    
                    if !vocabulary.romanization.isEmpty {
                        Text(vocabulary.romanization)
                            .font(.caption)
                            .foregroundColor(.secondary)
                            .italic()
                    }
                }
                
                Spacer()
                
                Button(action: { isExpanded.toggle() }) {
                    Image(systemName: isExpanded ? "chevron.up" : "chevron.down")
                        .foregroundColor(.secondary)
                }
            }
            
            if isExpanded {
                VStack(alignment: .leading, spacing: 8) {
                    if let example = vocabulary.exampleSentenceEnglish {
                        Text("Example: \(example)")
                            .font(.body)
                            .foregroundColor(.secondary)
                    }
                    
                    if let tamilExample = vocabulary.exampleSentenceTamil {
                        Text(tamilExample)
                            .font(.body)
                            .foregroundColor(.niraPrimary)
                    }
                    
                    if let notes = vocabulary.culturalNotes {
                        Text("Cultural Note: \(notes)")
                            .font(.caption)
                            .foregroundColor(.orange)
                            .padding(.top, 4)
                    }
                }
                .padding(.top, 8)
            }
        }
        .padding(12)
        .background(
            RoundedRectangle(cornerRadius: 8)
                .fill(Color(.systemGray6).opacity(0.5))
        )
    }
}

struct SupabaseConversationItemView: View {
    let conversation: TamilSupabaseConversation

    var body: some View {
        VStack(alignment: .leading, spacing: 8) {
            Text(conversation.titleEnglish)
                .font(.headline)
                .fontWeight(.semibold)

            Text(conversation.titleTamil)
                .font(.subheadline)
                .fontWeight(.regular)
                .foregroundColor(.secondary)

            if let context = conversation.contextDescription {
                Text(context)
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
        }
        .padding(12)
        .background(
            RoundedRectangle(cornerRadius: 8)
                .fill(Color(.systemGray6).opacity(0.5))
        )
    }
}

struct SupabaseGrammarItemView: View {
    let grammar: TamilSupabaseGrammarTopic

    var body: some View {
        VStack(alignment: .leading, spacing: 8) {
            Text(grammar.titleEnglish)
                .font(.headline)
                .fontWeight(.semibold)

            Text(grammar.titleTamil)
                .font(.subheadline)
                .fontWeight(.regular)
                .foregroundColor(.secondary)

            Text(grammar.ruleEnglish)
                .font(.body)
                .foregroundColor(.secondary)
        }
        .padding(12)
        .background(
            RoundedRectangle(cornerRadius: 8)
                .fill(Color(.systemGray6).opacity(0.5))
        )
    }
}

struct SupabasePracticeItemView: View {
    let exercise: TamilSupabasePracticeExercise
    
    var body: some View {
        VStack(alignment: .leading, spacing: 8) {
            HStack {
                Text(exercise.titleEnglish)
                    .font(.headline)
                    .fontWeight(.semibold)
                
                Spacer()
                
                Text(exercise.exerciseType.capitalized)
                    .font(.caption)
                    .fontWeight(.medium)
                    .foregroundColor(.white)
                    .padding(.horizontal, 8)
                    .padding(.vertical, 4)
                    .background(Color.purple)
                    .cornerRadius(6)
            }
            
            Text(exercise.instructionsEnglish)
                .font(.body)
                .foregroundColor(.secondary)
        }
        .padding(12)
        .background(
            RoundedRectangle(cornerRadius: 8)
                .fill(Color(.systemGray6).opacity(0.5))
        )
    }
}

#Preview {
    let sampleLesson = SupabaseLesson(
        id: UUID(),
        pathId: UUID(),
        title: "Basic Greetings",
        description: "Learn essential Tamil greetings and polite expressions",
        lessonType: "conversation",
        difficultyLevel: 1,
        estimatedDuration: 15,
        sequenceOrder: 1,
        learningObjectives: ["Learn basic greetings", "Use polite expressions"],
        vocabularyFocus: ["வணக்கம்", "நன்றி", "மன்னிக்கவும்"],
        grammarConcepts: ["Basic sentence structure"],
        culturalNotes: "Tamil greetings vary by time of day and formality",
        prerequisiteLessons: [],
        contentMetadata: SupabaseAnyCodable([:]),
        isActive: true,
        createdAt: Date(),
        updatedAt: Date(),
        audioUrl: nil,
        hasAudio: true,
        audioMetadata: nil
    )
    
    LessonDetailView(lesson: sampleLesson)
}
