import SwiftUI

// MARK: - NIRA Brand Components
// Professional branding system for consistent visual identity

struct NIRABrandComponents {
    
    // MARK: - Logo Components
    
    /// Primary NIRA logo with Tamil script
    struct PrimaryLogo: View {
        let size: CGFloat
        
        init(size: CGFloat = 100) {
            self.size = size
        }
        
        var body: some View {
            ZStack {
                // Background gradient circle
                Circle()
                    .fill(
                        RadialGradient(
                            colors: [Color.niraSecondary, Color.niraPrimary],
                            center: .center,
                            startRadius: 0,
                            endRadius: size/2
                        )
                    )
                    .frame(width: size, height: size)
                    .overlay(
                        Circle()
                            .stroke(Color.niraAccent, lineWidth: size * 0.03)
                    )
                
                // AI circuit pattern overlay
                Circle()
                    .fill(
                        RadialGradient(
                            colors: [Color.white.opacity(0.1), Color.clear],
                            center: .center,
                            startRadius: 0,
                            endRadius: size/3
                        )
                    )
                    .frame(width: size * 0.9, height: size * 0.9)
                
                // Tamil script "நீ"
                Text("நீ")
                    .font(.custom("Tamil Sangam MN", size: size * 0.4))
                    .fontWeight(.bold)
                    .foregroundColor(.white)
                    .shadow(color: .black.opacity(0.3), radius: 2, x: 1, y: 1)
            }
        }
    }
    
    /// Horizontal logo layout with text
    struct HorizontalLogo: View {
        let height: CGFloat
        
        init(height: CGFloat = 60) {
            self.height = height
        }
        
        var body: some View {
            HStack(spacing: height * 0.3) {
                // Icon
                PrimaryLogo(size: height)
                
                VStack(alignment: .leading, spacing: height * 0.05) {
                    // NIRA text
                    Text("NIRA")
                        .font(.custom("SF Pro Display", size: height * 0.4))
                        .fontWeight(.bold)
                        .foregroundColor(.niraPrimary)
                        .tracking(2)
                    
                    // Tamil subtitle
                    Text("தமிழ் கற்றல்")
                        .font(.custom("Tamil Sangam MN", size: height * 0.25))
                        .fontWeight(.medium)
                        .foregroundColor(.niraSecondary)
                    
                    // English subtitle
                    Text("HERITAGE LANGUAGE LEARNING")
                        .font(.custom("SF Pro Display", size: height * 0.15))
                        .fontWeight(.medium)
                        .foregroundColor(.secondary)
                        .tracking(1)
                }
            }
        }
    }
    
    /// Compact logo for navigation bars
    struct CompactLogo: View {
        let size: CGFloat
        
        init(size: CGFloat = 40) {
            self.size = size
        }
        
        var body: some View {
            HStack(spacing: size * 0.2) {
                // Simplified icon
                Circle()
                    .fill(
                        LinearGradient(
                            colors: [Color.niraSecondary, Color.niraPrimary],
                            startPoint: .topLeading,
                            endPoint: .bottomTrailing
                        )
                    )
                    .frame(width: size, height: size)
                    .overlay(
                        Text("நீ")
                            .font(.custom("Tamil Sangam MN", size: size * 0.5))
                            .fontWeight(.bold)
                            .foregroundColor(.white)
                    )
                
                // NIRA text
                Text("NIRA")
                    .font(.custom("SF Pro Display", size: size * 0.6))
                    .fontWeight(.bold)
                    .foregroundColor(.niraPrimary)
            }
        }
    }
    
    // MARK: - Icon Variations
    
    /// Monochrome version for specific contexts
    struct MonochromeLogo: View {
        let size: CGFloat
        let color: Color
        
        init(size: CGFloat = 100, color: Color = .primary) {
            self.size = size
            self.color = color
        }
        
        var body: some View {
            ZStack {
                Circle()
                    .fill(color)
                    .frame(width: size, height: size)
                
                Text("நீ")
                    .font(.custom("Tamil Sangam MN", size: size * 0.4))
                    .fontWeight(.bold)
                    .foregroundColor(.white)
            }
        }
    }
    
    /// Outlined version for light backgrounds
    struct OutlinedLogo: View {
        let size: CGFloat
        
        init(size: CGFloat = 100) {
            self.size = size
        }
        
        var body: some View {
            ZStack {
                Circle()
                    .stroke(
                        LinearGradient(
                            colors: [Color.niraSecondary, Color.niraPrimary],
                            startPoint: .topLeading,
                            endPoint: .bottomTrailing
                        ),
                        lineWidth: size * 0.05
                    )
                    .frame(width: size, height: size)
                
                Text("நீ")
                    .font(.custom("Tamil Sangam MN", size: size * 0.4))
                    .fontWeight(.bold)
                    .foregroundColor(.niraPrimary)
            }
        }
    }
}

// MARK: - Brand Colors Extension
extension Color {
    /// NIRA brand color palette - comprehensive system
    static let niraBrandSystem = NIRABrandColors()
}

struct NIRABrandColors {
    let primary = Color("NiraPrimary")      // #FF6B35
    let secondary = Color("NiraSecondary")  // #4ECDC4
    let accent = Color("NiraAccent")        // #FFE66D
    let success = Color("NiraSuccess")      // #87A96B
    let warning = Color("NiraWarning")      // #FFA726
    let error = Color("NiraError")          // #EF5350

    // Cultural colors
    let tamilRed = Color(red: 0.78, green: 0.15, blue: 0.24)     // #C7253E
    let templeGold = Color(red: 1.0, green: 0.9, blue: 0.43)    // #FFE66D
    let sageGreen = Color(red: 0.53, green: 0.66, blue: 0.42)   // #87A96B
}

// MARK: - Usage Examples & Preview
struct NIRABrandComponents_Previews: PreviewProvider {
    static var previews: some View {
        ScrollView {
            VStack(spacing: 30) {
                Group {
                    Text("NIRA Brand Components")
                        .font(.title)
                        .fontWeight(.bold)
                        .padding()
                    
                    // Primary Logo
                    VStack {
                        Text("Primary Logo")
                            .font(.headline)
                        NIRABrandComponents.PrimaryLogo(size: 120)
                    }
                    
                    // Horizontal Logo
                    VStack {
                        Text("Horizontal Logo")
                            .font(.headline)
                        NIRABrandComponents.HorizontalLogo(height: 80)
                    }
                    
                    // Compact Logo
                    VStack {
                        Text("Compact Logo")
                            .font(.headline)
                        NIRABrandComponents.CompactLogo(size: 50)
                    }
                }
                
                Group {
                    // Monochrome Variations
                    VStack {
                        Text("Monochrome Variations")
                            .font(.headline)
                        HStack(spacing: 20) {
                            NIRABrandComponents.MonochromeLogo(size: 60, color: .black)
                            NIRABrandComponents.MonochromeLogo(size: 60, color: .gray)
                            NIRABrandComponents.MonochromeLogo(size: 60, color: .niraPrimary)
                        }
                    }
                    
                    // Outlined Version
                    VStack {
                        Text("Outlined Logo")
                            .font(.headline)
                        NIRABrandComponents.OutlinedLogo(size: 80)
                    }
                    
                    // Size Variations
                    VStack {
                        Text("Size Variations")
                            .font(.headline)
                        HStack(spacing: 15) {
                            NIRABrandComponents.PrimaryLogo(size: 40)
                            NIRABrandComponents.PrimaryLogo(size: 60)
                            NIRABrandComponents.PrimaryLogo(size: 80)
                            NIRABrandComponents.PrimaryLogo(size: 100)
                        }
                    }
                }
            }
            .padding()
        }
        .background(Color(.systemGroupedBackground))
    }
}

// MARK: - Brand Usage Guidelines
/*
 NIRA Brand Usage Guidelines:
 
 1. PRIMARY LOGO
    - Use for main branding, splash screens, about pages
    - Minimum size: 40x40 points
    - Maintain clear space of 1/4 logo height around logo
 
 2. HORIZONTAL LOGO
    - Use for headers, navigation, marketing materials
    - Ideal for wide layouts and business cards
    - Minimum height: 30 points
 
 3. COMPACT LOGO
    - Use for navigation bars, small spaces
    - Maintains readability at small sizes
    - Minimum size: 24x24 points
 
 4. MONOCHROME VERSIONS
    - Use when color reproduction is limited
    - Maintain contrast ratios for accessibility
    - Available in black, white, and brand colors
 
 5. COLOR GUIDELINES
    - Primary: #FF6B35 (vibrant orange-red)
    - Secondary: #4ECDC4 (teal blue)
    - Accent: #FFE66D (golden yellow)
    - Always test contrast ratios (minimum 4.5:1)
 
 6. CULTURAL SENSITIVITY
    - Tamil script "நீ" is central to brand identity
    - Ensure proper Tamil font rendering
    - Respect cultural significance of colors and symbols
    - Consult Tamil speakers for cultural accuracy
 */
