import SwiftUI

struct GrammarDetailModal: View {
    let grammarTopics: [TamilSupabaseGrammarTopic]
    @Binding var selectedIndex: Int
    let level: CEFRLevel
    @Environment(\.dismiss) private var dismiss
    
    private var levelColor: Color {
        switch level {
        case .a1: return .green
        case .a2: return .blue
        case .b1: return .orange
        case .b2: return .purple
        case .c1: return .red
        case .c2: return .indigo
        }
    }
    
    private var levelGradient: LinearGradient {
        LinearGradient(
            colors: [levelColor, levelColor.opacity(0.7)],
            startPoint: .topLeading,
            endPoint: .bottomTrailing
        )
    }
    
    var body: some View {
        NavigationView {
            ZStack {
                // Background with glassmorphism
                LinearGradient(
                    colors: [
                        Color(.systemBackground),
                        levelColor.opacity(0.05)
                    ],
                    startPoint: .topLeading,
                    endPoint: .bottomTrailing
                )
                .ignoresSafeArea()
                
                // Floating geometric patterns
                ForEach(0..<3, id: \.self) { index in
                    Circle()
                        .fill(levelColor.opacity(0.03))
                        .frame(width: CGFloat.random(in: 100...200))
                        .position(
                            x: CGFloat.random(in: 50...350),
                            y: CGFloat.random(in: 100...600)
                        )
                        .animation(
                            .easeInOut(duration: Double.random(in: 4...8))
                            .repeatForever(autoreverses: true)
                            .delay(Double(index) * 0.5),
                            value: selectedIndex
                        )
                }
                
                VStack(spacing: 0) {
                    // Header
                    HStack {
                        Button("Close") {
                            dismiss()
                        }
                        .foregroundColor(levelColor)
                        .fontWeight(.medium)
                        
                        Spacer()
                        
                        Text("Grammar")
                            .font(.headline)
                            .fontWeight(.semibold)
                        
                        Spacer()
                        
                        Text("\(selectedIndex + 1)/\(grammarTopics.count)")
                            .font(.subheadline)
                            .foregroundColor(.secondary)
                    }
                    .padding(.horizontal, 24)
                    .padding(.vertical, 16)
                    .background(.ultraThinMaterial)
                    
                    // Content with TabView for swipe navigation
                    TabView(selection: $selectedIndex) {
                        ForEach(Array(grammarTopics.enumerated()), id: \.offset) { index, grammar in
                            SupabaseGrammarDetailCard(
                                grammar: grammar,
                                level: level,
                                levelColor: levelColor,
                                levelGradient: levelGradient
                            )
                            .tag(index)
                        }
                    }
                    .tabViewStyle(.page(indexDisplayMode: .never))
                    .animation(.easeInOut(duration: 0.3), value: selectedIndex)
                    
                    // Progress indicator
                    HStack(spacing: 8) {
                        ForEach(0..<grammarTopics.count, id: \.self) { index in
                            Circle()
                                .fill(index == selectedIndex ? levelColor : Color.secondary.opacity(0.3))
                                .frame(width: 8, height: 8)
                                .scaleEffect(index == selectedIndex ? 1.2 : 1.0)
                                .animation(.easeInOut(duration: 0.2), value: selectedIndex)
                        }
                    }
                    .padding(.vertical, 16)
                    .background(.ultraThinMaterial)
                }
            }
        }
        .navigationBarHidden(true)
    }
}

struct SupabaseGrammarDetailCard: View {
    let grammar: TamilSupabaseGrammarTopic
    let level: CEFRLevel
    let levelColor: Color
    let levelGradient: LinearGradient
    @State private var showingExplanation = false
    @State private var showingTips = false
    @State private var showingMistakes = false
    
    var body: some View {
        ScrollView {
            VStack(spacing: 24) {
                // Main grammar topic with glassmorphism
                VStack(spacing: 20) {
                    // Grammar topic titles
                    VStack(spacing: 12) {
                        Text(grammar.titleEnglish)
                            .font(.title2)
                            .fontWeight(.semibold)
                            .foregroundColor(.primary)
                            .multilineTextAlignment(.center)

                        Text(grammar.titleTamil)
                            .font(.title3)
                            .fontWeight(.regular)
                            .foregroundColor(.secondary)
                            .multilineTextAlignment(.center)
                    }
                    
                    // Difficulty indicator
                    HStack {
                        Text("Difficulty Level")
                            .font(.caption)
                            .foregroundColor(.secondary)
                        
                        HStack(spacing: 4) {
                            ForEach(1...5, id: \.self) { level in
                                Circle()
                                    .fill(level <= grammar.difficultyLevel ? levelColor : Color.secondary.opacity(0.3))
                                    .frame(width: 8, height: 8)
                            }
                        }
                        
                        Spacer()
                        
                        Text("Level \(grammar.difficultyLevel)")
                            .font(.caption)
                            .fontWeight(.medium)
                            .foregroundColor(levelColor)
                            .padding(.horizontal, 8)
                            .padding(.vertical, 4)
                            .background(levelColor.opacity(0.1))
                            .cornerRadius(8)
                    }
                }
                .padding(24)
                .background(
                    RoundedRectangle(cornerRadius: 24)
                        .fill(.ultraThinMaterial)
                        .overlay(
                            RoundedRectangle(cornerRadius: 24)
                                .stroke(
                                    LinearGradient(
                                        colors: [Color.white.opacity(0.3), Color.white.opacity(0.1)],
                                        startPoint: .topLeading,
                                        endPoint: .bottomTrailing
                                    ),
                                    lineWidth: 1
                                )
                        )
                )
                .shadow(color: levelColor.opacity(0.1), radius: 12, x: 0, y: 6)
                
                // Grammar rules section
                VStack(alignment: .leading, spacing: 16) {
                    HStack {
                        Image(systemName: "book.fill")
                            .foregroundColor(levelColor)
                        
                        Text("Grammar Rule")
                            .font(.headline)
                            .fontWeight(.semibold)
                            .foregroundColor(.primary)
                        
                        Spacer()
                    }
                    
                    VStack(alignment: .leading, spacing: 12) {
                        VStack(alignment: .leading, spacing: 8) {
                            Text("English:")
                                .font(.subheadline)
                                .fontWeight(.semibold)
                                .foregroundColor(.secondary)
                            
                            Text(grammar.ruleEnglish)
                                .font(.body)
                                .foregroundColor(.primary)
                        }
                        
                        Divider()
                        
                        VStack(alignment: .leading, spacing: 8) {
                            Text("Tamil:")
                                .font(.subheadline)
                                .fontWeight(.semibold)
                                .foregroundColor(.secondary)
                            
                            Text(grammar.ruleTamil)
                                .font(.body)
                                .fontWeight(.medium)
                                .foregroundColor(levelColor)
                        }
                    }
                }
                .padding(20)
                .background(
                    RoundedRectangle(cornerRadius: 20)
                        .fill(.ultraThinMaterial)
                        .overlay(
                            RoundedRectangle(cornerRadius: 20)
                                .stroke(levelColor.opacity(0.2), lineWidth: 1)
                        )
                )
                .shadow(color: levelColor.opacity(0.05), radius: 8, x: 0, y: 4)
                
                // Explanation section
                if let explanation = grammar.explanation, !explanation.isEmpty {
                    VStack(alignment: .leading, spacing: 16) {
                        Button {
                            withAnimation(.easeInOut(duration: 0.3)) {
                                showingExplanation.toggle()
                            }
                        } label: {
                            HStack {
                                Image(systemName: "lightbulb.fill")
                                    .foregroundColor(.blue)
                                
                                Text("Detailed Explanation")
                                    .font(.headline)
                                    .fontWeight(.semibold)
                                    .foregroundColor(.primary)
                                
                                Spacer()
                                
                                Image(systemName: showingExplanation ? "chevron.up.circle.fill" : "chevron.down.circle.fill")
                                    .foregroundColor(.blue)
                                    .font(.title3)
                            }
                        }
                        
                        if showingExplanation {
                            Text(explanation)
                                .font(.body)
                                .foregroundColor(.primary)
                                .transition(.opacity.combined(with: .move(edge: .top)))
                        }
                    }
                    .padding(20)
                    .background(
                        RoundedRectangle(cornerRadius: 20)
                            .fill(.ultraThinMaterial)
                            .overlay(
                                RoundedRectangle(cornerRadius: 20)
                                    .stroke(Color.blue.opacity(0.2), lineWidth: 1)
                            )
                    )
                    .shadow(color: Color.blue.opacity(0.05), radius: 8, x: 0, y: 4)
                }
                
                // Tips section
                if let tips = grammar.tips, !tips.isEmpty {
                    VStack(alignment: .leading, spacing: 16) {
                        Button {
                            withAnimation(.easeInOut(duration: 0.3)) {
                                showingTips.toggle()
                            }
                        } label: {
                            HStack {
                                Image(systemName: "star.fill")
                                    .foregroundColor(.orange)
                                
                                Text("Helpful Tips")
                                    .font(.headline)
                                    .fontWeight(.semibold)
                                    .foregroundColor(.primary)
                                
                                Spacer()
                                
                                Image(systemName: showingTips ? "chevron.up.circle.fill" : "chevron.down.circle.fill")
                                    .foregroundColor(.orange)
                                    .font(.title3)
                            }
                        }
                        
                        if showingTips {
                            VStack(alignment: .leading, spacing: 8) {
                                ForEach(tips.prefix(5), id: \.self) { tip in
                                    HStack(alignment: .top, spacing: 12) {
                                        Image(systemName: "checkmark.circle.fill")
                                            .foregroundColor(.orange)
                                            .font(.callout)
                                        
                                        Text(tip)
                                            .font(.callout)
                                            .foregroundColor(.primary)
                                    }
                                }
                            }
                            .transition(.opacity.combined(with: .move(edge: .top)))
                        }
                    }
                    .padding(20)
                    .background(
                        RoundedRectangle(cornerRadius: 20)
                            .fill(.ultraThinMaterial)
                            .overlay(
                                RoundedRectangle(cornerRadius: 20)
                                    .stroke(Color.orange.opacity(0.2), lineWidth: 1)
                            )
                    )
                    .shadow(color: Color.orange.opacity(0.05), radius: 8, x: 0, y: 4)
                }
                
                // Common mistakes section
                if let mistakes = grammar.commonMistakes, !mistakes.isEmpty {
                    VStack(alignment: .leading, spacing: 16) {
                        Button {
                            withAnimation(.easeInOut(duration: 0.3)) {
                                showingMistakes.toggle()
                            }
                        } label: {
                            HStack {
                                Image(systemName: "exclamationmark.triangle.fill")
                                    .foregroundColor(.red)
                                
                                Text("Common Mistakes")
                                    .font(.headline)
                                    .fontWeight(.semibold)
                                    .foregroundColor(.primary)
                                
                                Spacer()
                                
                                Image(systemName: showingMistakes ? "chevron.up.circle.fill" : "chevron.down.circle.fill")
                                    .foregroundColor(.red)
                                    .font(.title3)
                            }
                        }
                        
                        if showingMistakes {
                            VStack(alignment: .leading, spacing: 8) {
                                ForEach(mistakes.prefix(5), id: \.self) { mistake in
                                    HStack(alignment: .top, spacing: 12) {
                                        Image(systemName: "xmark.circle.fill")
                                            .foregroundColor(.red)
                                            .font(.callout)
                                        
                                        Text(mistake)
                                            .font(.callout)
                                            .foregroundColor(.primary)
                                    }
                                }
                            }
                            .transition(.opacity.combined(with: .move(edge: .top)))
                        }
                    }
                    .padding(20)
                    .background(
                        RoundedRectangle(cornerRadius: 20)
                            .fill(.ultraThinMaterial)
                            .overlay(
                                RoundedRectangle(cornerRadius: 20)
                                    .stroke(Color.red.opacity(0.2), lineWidth: 1)
                            )
                    )
                    .shadow(color: Color.red.opacity(0.05), radius: 8, x: 0, y: 4)
                }
            }
            .padding(.horizontal, 20)
            .padding(.vertical, 16)
        }
    }
}
