//
//  AgentsView.swift
//  NIRA
//
//  Enhanced AI Agents Dashboard with Modern UX
//  Based on Language Learning Content Development Standards v2.0
//

import SwiftUI

struct AgentsView: View {
    // Single Tamil AI Assist
    private var tamilAIAssist: Agent {
        Agent(
            id: "tamil_ai_assist",
            name: "Tamil AI Assist",
            description: "Your comprehensive Tamil learning companion. Get help with conversations, grammar, literature, cultural insights, and more.",
            expertise: ["Conversations", "Grammar", "Literature", "Culture", "Pronunciation", "Writing"],
            rating: 4.9,
            isOnline: true,
            profileImageName: "agent_tamil_ai",
            emoji: "🤖"
        )
    }

    var body: some View {
        // Direct chat interface - no intermediate page
        AIAgentChatView(agent: convertToLanguageTutor(tamilAIAssist))
            .navigationBarHidden(true)
    }



    private func convertToLanguageTutor(_ agent: Agent) -> LanguageTutor {
        return LanguageTutor(
            persona: .beginnerEnthusiast, // This could be dynamic based on agent type
            language: .tamil,
            name: agent.name,
            avatar: agent.emoji,
            description: agent.description,
            specialties: agent.expertise,
            systemPrompt: "You are \(agent.name), a Tamil language learning assistant specializing in \(agent.expertise.joined(separator: ", ")). Help users learn Tamil in a friendly and encouraging way."
        )
    }
}

// MARK: - Agent Model
struct Agent {
    let id: String
    let name: String
    let description: String
    let expertise: [String]
    let rating: Double
    let isOnline: Bool
    let profileImageName: String
    let emoji: String
}

#Preview {
    AgentsView()
}
