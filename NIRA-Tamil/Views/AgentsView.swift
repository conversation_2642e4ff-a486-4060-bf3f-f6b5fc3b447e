//
//  AgentsView.swift
//  NIRA
//
//  Enhanced AI Agents Dashboard with Modern UX
//  Based on Language Learning Content Development Standards v2.0
//

import SwiftUI

struct AgentsView: View {
    @State private var showingTamilAssist = false
    @State private var isLoading = false

    // Single Tamil AI Assist
    private var tamilAIAssist: Agent {
        Agent(
            id: "tamil_ai_assist",
            name: "Tamil AI Assist",
            description: "Your comprehensive Tamil learning companion. Get help with conversations, grammar, literature, cultural insights, and more.",
            expertise: ["Conversations", "Grammar", "Literature", "Culture", "Pronunciation", "Writing"],
            rating: 4.9,
            isOnline: true,
            profileImageName: "agent_tamil_ai",
            emoji: "🤖"
        )
    }


    var body: some View {
        VStack(spacing: 0) {
            // Modern Header
            modernHeaderView

            // Main Content
            ScrollView {
                VStack(spacing: 24) {
                    // Hero Section
                    heroSectionView

                    // Single Tamil AI Assist Card
                    tamilAssistCard
                }
                .padding(.horizontal, 16)
                .padding(.bottom, 100)
            }
        }
        .navigationBarHidden(true)
        .background(Color(.systemBackground))
        .sheet(isPresented: $showingTamilAssist) {
            AIAgentChatView(agent: convertToLanguageTutor(tamilAIAssist))
        }
    }



    private func convertToLanguageTutor(_ agent: Agent) -> LanguageTutor {
        return LanguageTutor(
            persona: .beginnerEnthusiast, // This could be dynamic based on agent type
            language: .tamil,
            name: agent.name,
            avatar: agent.emoji,
            description: agent.description,
            specialties: agent.expertise,
            systemPrompt: "You are \(agent.name), a Tamil language learning assistant specializing in \(agent.expertise.joined(separator: ", ")). Help users learn Tamil in a friendly and encouraging way."
        )
    }
    
    // MARK: - Header View
    private var modernHeaderView: some View {
        HStack {
            VStack(alignment: .leading, spacing: 2) {
                Text("Tamil AI Assist")
                    .font(.system(size: 24, weight: .bold, design: .rounded))
                    .foregroundColor(.primary)
                Text("Your comprehensive Tamil learning companion")
                    .font(.caption)
                    .foregroundColor(.secondary)
            }

            Spacer()

            // Tamil AI symbol
            Text("🤖")
                .font(.title2)
        }
        .padding(.horizontal, 16)
        .padding(.vertical, 8)
    }
    
    // MARK: - Intro Section
    private var heroSectionView: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("Your Tamil Learning Assistant")
                .font(.headline)
                .fontWeight(.semibold)
                .foregroundColor(.primary)

            Text("Get comprehensive help with conversations, grammar, literature, culture, and more")
                .font(.subheadline)
                .foregroundColor(.secondary)
        }
        .frame(maxWidth: .infinity, alignment: .leading)
        .padding(.top, 16)
    }

    // MARK: - Tamil Assist Card
    private var tamilAssistCard: some View {
        Button(action: {
            showingTamilAssist = true
        }) {
            VStack(spacing: 16) {
                // Header with emoji and title
                HStack {
                    Text("🤖")
                        .font(.system(size: 40))

                    VStack(alignment: .leading, spacing: 4) {
                        Text("Tamil AI Assist")
                            .font(.title2)
                            .fontWeight(.bold)
                            .foregroundColor(.primary)

                        HStack {
                            Circle()
                                .fill(.green)
                                .frame(width: 8, height: 8)
                            Text("Online")
                                .font(.caption)
                                .foregroundColor(.secondary)
                        }
                    }

                    Spacer()

                    // Rating
                    HStack(spacing: 2) {
                        Image(systemName: "star.fill")
                            .foregroundColor(.yellow)
                            .font(.caption)
                        Text("4.9")
                            .font(.caption)
                            .fontWeight(.medium)
                    }
                }

                // Description
                Text(tamilAIAssist.description)
                    .font(.subheadline)
                    .foregroundColor(.secondary)
                    .multilineTextAlignment(.leading)
                    .frame(maxWidth: .infinity, alignment: .leading)

                // Expertise tags
                LazyVGrid(columns: Array(repeating: GridItem(.flexible()), count: 3), spacing: 8) {
                    ForEach(tamilAIAssist.expertise, id: \.self) { skill in
                        Text(skill)
                            .font(.caption)
                            .padding(.horizontal, 8)
                            .padding(.vertical, 4)
                            .background(Color.blue.opacity(0.1))
                            .foregroundColor(.blue)
                            .clipShape(Capsule())
                    }
                }

                // Start chat button
                HStack {
                    Spacer()
                    Text("Start Conversation")
                        .font(.subheadline)
                        .fontWeight(.medium)
                        .foregroundColor(.white)
                        .padding(.horizontal, 20)
                        .padding(.vertical, 10)
                        .background(
                            LinearGradient(
                                colors: [.blue, .purple],
                                startPoint: .leading,
                                endPoint: .trailing
                            )
                        )
                        .clipShape(Capsule())
                    Spacer()
                }
            }
            .padding(20)
            .background(
                RoundedRectangle(cornerRadius: 16)
                    .fill(.ultraThinMaterial)
                    .background(
                        RoundedRectangle(cornerRadius: 16)
                            .fill(
                                LinearGradient(
                                    colors: [
                                        Color.blue.opacity(0.1),
                                        Color.purple.opacity(0.1)
                                    ],
                                    startPoint: .topLeading,
                                    endPoint: .bottomTrailing
                                )
                            )
                    )
            )
            .overlay(
                RoundedRectangle(cornerRadius: 16)
                    .stroke(
                        LinearGradient(
                            colors: [
                                Color.blue.opacity(0.3),
                                Color.purple.opacity(0.3)
                            ],
                            startPoint: .leading,
                            endPoint: .trailing
                        ),
                        lineWidth: 1
                    )
            )
        }
        .buttonStyle(PlainButtonStyle())
    }
}

// MARK: - Agent Model
struct Agent {
    let id: String
    let name: String
    let description: String
    let expertise: [String]
    let rating: Double
    let isOnline: Bool
    let profileImageName: String
    let emoji: String
}

#Preview {
    AgentsView()
}
