import SwiftUI

struct CultureCategoryView: View {
    let category: EnhancedCulturalCategory
    @StateObject private var cultureService = EnhancedCultureService.shared
    @Environment(\.dismiss) private var dismiss
    
    var body: some View {
        NavigationView {
            ScrollView {
                LazyVStack(spacing: 16) {
                    // Header
                    headerSection

                    // Placeholder content
                    Text("Content for \(category.name) will be displayed here")
                        .font(.body)
                        .foregroundColor(.secondary)
                        .padding()
                        .background(.ultraThinMaterial)
                        .clipShape(RoundedRectangle(cornerRadius: 16))
                }
                .padding()
            }
            .background(
                LinearGradient(
                    colors: [
                        Color(category.colorTheme).opacity(0.1),
                        Color.clear
                    ],
                    startPoint: .top,
                    endPoint: .bottom
                )
            )
            .navigationTitle(category.name)
            .navigationBarTitleDisplayMode(.large)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("Done") {
                        dismiss()
                    }
                }
            }
        }
    }
    
    private var headerSection: some View {
        VStack(spacing: 12) {
            // Category Icon
            Text(category.emoji)
                .font(.system(size: 60))
                .padding()
                .background(.ultraThinMaterial)
                .clipShape(Circle())
                .overlay(
                    Circle()
                        .stroke(.white.opacity(0.3), lineWidth: 1)
                )
            
            // Category Info
            VStack(spacing: 8) {
                Text(category.name)
                    .font(.title)
                    .fontWeight(.bold)
                
                Text(category.nameTamil)
                    .font(.title2)
                    .foregroundColor(.secondary)
                
                if let description = category.description {
                    Text(description)
                        .font(.body)
                        .foregroundColor(.secondary)
                        .multilineTextAlignment(.center)
                        .padding(.horizontal)
                }
                
                // Content Count Badge
                Text("\(category.contentCount) Items")
                    .font(.caption)
                    .fontWeight(.semibold)
                    .foregroundColor(.white)
                    .padding(.horizontal, 12)
                    .padding(.vertical, 6)
                    .background(Color(category.colorTheme))
                    .clipShape(Capsule())
            }
        }
        .padding()
        .background(.ultraThinMaterial)
        .clipShape(RoundedRectangle(cornerRadius: 20))
    }
    

}
