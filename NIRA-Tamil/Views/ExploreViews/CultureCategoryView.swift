import SwiftUI

struct CultureCategoryView: View {
    let category: EnhancedCulturalCategory
    @StateObject private var cultureService = EnhancedCultureService.shared
    @Environment(\.dismiss) private var dismiss
    @State private var contentItems: [Any] = []

    var body: some View {
        ZStack {
            // Dark background gradient matching the screenshots
            LinearGradient(
                colors: [
                    Color.black,
                    Color(red: 0.1, green: 0.1, blue: 0.1),
                    Color.black
                ],
                startPoint: .topLeading,
                endPoint: .bottomTrailing
            )
            .ignoresSafeArea()

            ScrollView {
                VStack(spacing: 24) {
                    // Header section with glassmorphic design
                    headerSection

                    // Content grid
                    contentGrid
                }
                .padding(.horizontal, 20)
                .padding(.top, 20)
                .padding(.bottom, 100)
            }
        }
        .navigationBarTitleDisplayMode(.inline)
        .navigationTitle(category.name)
        .toolbar {
            ToolbarItem(placement: .navigationBarTrailing) {
                Button("Done") {
                    dismiss()
                }
                .foregroundColor(.blue)
                .font(.system(size: 16, weight: .medium))
            }
        }
        .toolbarBackground(.clear, for: .navigationBar)
        .onAppear {
            loadContent()
        }
    }

    private var headerSection: some View {
        VStack(spacing: 20) {
            // Glassmorphic header card matching the screenshots
            VStack(spacing: 16) {
                // Category Icon with glassmorphic circle
                ZStack {
                    Circle()
                        .fill(
                            LinearGradient(
                                colors: [
                                    Color.white.opacity(0.15),
                                    Color.white.opacity(0.05)
                                ],
                                startPoint: .topLeading,
                                endPoint: .bottomTrailing
                            )
                        )
                        .frame(width: 80, height: 80)
                        .overlay(
                            Circle()
                                .stroke(
                                    LinearGradient(
                                        colors: [
                                            Color.white.opacity(0.3),
                                            Color.clear
                                        ],
                                        startPoint: .topLeading,
                                        endPoint: .bottomTrailing
                                    ),
                                    lineWidth: 1
                                )
                        )

                    Text(category.emoji)
                        .font(.system(size: 36))
                }

                // Category Title
                Text(category.name)
                    .font(.system(size: 32, weight: .bold, design: .default))
                    .foregroundColor(.white)

                // Tamil text
                Text(category.nameTamil)
                    .font(.system(size: 18, weight: .medium))
                    .foregroundColor(.gray)

                // Category Description
                if let description = category.description {
                    Text(description)
                        .font(.system(size: 16, weight: .regular))
                        .foregroundColor(.gray)
                        .multilineTextAlignment(.center)
                        .lineLimit(3)
                }

                // Item Count
                Text("\(contentItems.count) Items")
                    .font(.system(size: 14, weight: .medium))
                    .foregroundColor(.white)
                    .padding(.horizontal, 16)
                    .padding(.vertical, 8)
                    .background(
                        Capsule()
                            .fill(
                                LinearGradient(
                                    colors: [
                                        Color.white.opacity(0.2),
                                        Color.white.opacity(0.1)
                                    ],
                                    startPoint: .leading,
                                    endPoint: .trailing
                                )
                            )
                            .overlay(
                                Capsule()
                                    .stroke(Color.white.opacity(0.3), lineWidth: 1)
                            )
                    )
            }
            .padding(24)
            .background(
                RoundedRectangle(cornerRadius: 20)
                    .fill(
                        LinearGradient(
                            colors: [
                                Color.white.opacity(0.15),
                                Color.white.opacity(0.05)
                            ],
                            startPoint: .topLeading,
                            endPoint: .bottomTrailing
                        )
                    )
                    .overlay(
                        RoundedRectangle(cornerRadius: 20)
                            .stroke(
                                LinearGradient(
                                    colors: [
                                        Color.white.opacity(0.3),
                                        Color.clear
                                    ],
                                    startPoint: .topLeading,
                                    endPoint: .bottomTrailing
                                ),
                                lineWidth: 1
                            )
                    )
            )
            .shadow(color: .black.opacity(0.3), radius: 20, x: 0, y: 10)
        }
    }

    // MARK: - Content Grid
    private var contentGrid: some View {
        LazyVGrid(columns: [
            GridItem(.flexible()),
            GridItem(.flexible())
        ], spacing: 16) {
            ForEach(Array(contentItems.enumerated()), id: \.offset) { index, item in
                contentCard(for: item, index: index)
            }
        }
    }

    // MARK: - Content Card
    @ViewBuilder
    private func contentCard(for item: Any, index: Int) -> some View {
        VStack(alignment: .leading, spacing: 12) {
            // Icon/Image placeholder with glassmorphic design
            ZStack {
                RoundedRectangle(cornerRadius: 12)
                    .fill(
                        LinearGradient(
                            colors: [
                                Color.white.opacity(0.1),
                                Color.white.opacity(0.05)
                            ],
                            startPoint: .topLeading,
                            endPoint: .bottomTrailing
                        )
                    )
                    .frame(height: 100)
                    .overlay(
                        RoundedRectangle(cornerRadius: 12)
                            .stroke(Color.white.opacity(0.2), lineWidth: 1)
                    )

                Text(category.emoji)
                    .font(.system(size: 28))
            }

            VStack(alignment: .leading, spacing: 6) {
                Text(getItemTitle(item))
                    .font(.system(size: 16, weight: .semibold))
                    .foregroundColor(.white)
                    .lineLimit(2)

                Text(getItemDescription(item))
                    .font(.system(size: 12, weight: .regular))
                    .foregroundColor(.gray)
                    .lineLimit(3)
            }
        }
        .padding(16)
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(
                    LinearGradient(
                        colors: [
                            Color.white.opacity(0.1),
                            Color.white.opacity(0.05)
                        ],
                        startPoint: .topLeading,
                        endPoint: .bottomTrailing
                    )
                )
                .overlay(
                    RoundedRectangle(cornerRadius: 16)
                        .stroke(
                            LinearGradient(
                                colors: [
                                    Color.white.opacity(0.2),
                                    Color.clear
                                ],
                                startPoint: .topLeading,
                                endPoint: .bottomTrailing
                            ),
                            lineWidth: 1
                        )
                )
        )
        .shadow(color: .black.opacity(0.2), radius: 10, x: 0, y: 5)
        .onTapGesture {
            // Handle item tap
            print("Tapped item: \(getItemTitle(item))")
        }
    }

    // MARK: - Helper Methods
    private func loadContent() {
        contentItems = cultureService.getContent(for: category.name)
        print("📱 Loaded \(contentItems.count) items for \(category.name)")
    }

    private func getTamilName() -> String? {
        return category.nameTamil
    }

    private func getItemTitle(_ item: Any) -> String {
        if let cinema = item as? CinemaContent {
            return cinema.title
        } else if let music = item as? MusicContent {
            return music.title
        } else if let festival = item as? FestivalsContent {
            return festival.title
        } else if let architecture = item as? ArchitectureContent {
            return architecture.title
        } else if let cuisine = item as? CuisineContent {
            return cuisine.title
        } else if let sports = item as? SportsContent {
            return sports.name
        } else if let arts = item as? ArtsDanceContent {
            return arts.title
        }
        return "Unknown Item"
    }

    private func getItemDescription(_ item: Any) -> String {
        if let cinema = item as? CinemaContent {
            return cinema.description
        } else if let music = item as? MusicContent {
            return music.description
        } else if let festival = item as? FestivalsContent {
            return festival.description
        } else if let architecture = item as? ArchitectureContent {
            return architecture.description
        } else if let cuisine = item as? CuisineContent {
            return cuisine.description
        } else if let sports = item as? SportsContent {
            return sports.description
        } else if let arts = item as? ArtsDanceContent {
            return arts.description
        }
        return "No description available"
    }
}
