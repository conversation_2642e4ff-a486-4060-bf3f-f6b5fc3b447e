import SwiftUI

struct CultureCategoryView: View {
    let category: EnhancedCulturalCategory
    @StateObject private var cultureService = EnhancedCultureService.shared
    @Environment(\.dismiss) private var dismiss
    @State private var contentItems: [Any] = []
    
    var body: some View {
        NavigationView {
            ScrollView {
                LazyVStack(spacing: 16) {
                    // Header
                    headerSection

                    // Content Grid
                    contentGrid
                }
                .padding()
            }
            .background(
                LinearGradient(
                    colors: [
                        Color(category.colorTheme).opacity(0.1),
                        Color.clear
                    ],
                    startPoint: .top,
                    endPoint: .bottom
                )
            )
            .navigationTitle(category.name)
            .navigationBarTitleDisplayMode(.large)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("Done") {
                        dismiss()
                    }
                }
            }
        }
    }
    
    private var headerSection: some View {
        VStack(spacing: 12) {
            // Category Icon
            Text(category.emoji)
                .font(.system(size: 60))
                .padding()
                .background(.ultraThinMaterial)
                .clipShape(Circle())
                .overlay(
                    Circle()
                        .stroke(.white.opacity(0.3), lineWidth: 1)
                )
            
            // Category Info
            VStack(spacing: 8) {
                Text(category.name)
                    .font(.title)
                    .fontWeight(.bold)
                
                Text(category.nameTamil)
                    .font(.title2)
                    .foregroundColor(.secondary)
                
                if let description = category.description {
                    Text(description)
                        .font(.body)
                        .foregroundColor(.secondary)
                        .multilineTextAlignment(.center)
                        .padding(.horizontal)
                }
                
                // Content Count Badge
                Text("\(category.contentCount) Items")
                    .font(.caption)
                    .fontWeight(.semibold)
                    .foregroundColor(.white)
                    .padding(.horizontal, 12)
                    .padding(.vertical, 6)
                    .background(Color(category.colorTheme))
                    .clipShape(Capsule())
            }
        }
        .padding()
        .background(.ultraThinMaterial)
        .clipShape(RoundedRectangle(cornerRadius: 20))
    }

    // MARK: - Content Grid
    private var contentGrid: some View {
        LazyVGrid(columns: [
            GridItem(.flexible()),
            GridItem(.flexible())
        ], spacing: 16) {
            ForEach(Array(contentItems.enumerated()), id: \.offset) { index, item in
                contentCard(for: item, index: index)
            }
        }
        .padding(.horizontal)
        .onAppear {
            loadContent()
        }
    }

    // MARK: - Content Card
    @ViewBuilder
    private func contentCard(for item: Any, index: Int) -> some View {
        VStack(alignment: .leading, spacing: 12) {
            // Icon/Image placeholder
            ZStack {
                RoundedRectangle(cornerRadius: 12)
                    .fill(Color(category.colorTheme).opacity(0.2))
                    .frame(height: 120)

                Text(category.emoji)
                    .font(.system(size: 32))
            }

            VStack(alignment: .leading, spacing: 4) {
                Text(getItemTitle(item))
                    .font(.headline)
                    .foregroundColor(.primary)
                    .lineLimit(2)

                Text(getItemDescription(item))
                    .font(.caption)
                    .foregroundColor(.secondary)
                    .lineLimit(3)
            }
        }
        .padding()
        .background(.ultraThinMaterial)
        .clipShape(RoundedRectangle(cornerRadius: 16))
        .onTapGesture {
            // Handle item tap
            print("Tapped item: \(getItemTitle(item))")
        }
    }

    // MARK: - Helper Methods
    private func loadContent() {
        contentItems = cultureService.getContent(for: category.name)
        print("📱 Loaded \(contentItems.count) items for \(category.name)")
    }

    private func getItemTitle(_ item: Any) -> String {
        if let cinema = item as? CinemaContent {
            return cinema.title
        } else if let music = item as? MusicContent {
            return music.title
        } else if let festival = item as? FestivalsContent {
            return festival.title
        } else if let architecture = item as? ArchitectureContent {
            return architecture.title
        } else if let cuisine = item as? CuisineContent {
            return cuisine.title
        } else if let sports = item as? SportsContent {
            return sports.name
        } else if let arts = item as? ArtsDanceContent {
            return arts.title
        }
        return "Unknown Item"
    }

    private func getItemDescription(_ item: Any) -> String {
        if let cinema = item as? CinemaContent {
            return cinema.description
        } else if let music = item as? MusicContent {
            return music.description
        } else if let festival = item as? FestivalsContent {
            return festival.description
        } else if let architecture = item as? ArchitectureContent {
            return architecture.description
        } else if let cuisine = item as? CuisineContent {
            return cuisine.description
        } else if let sports = item as? SportsContent {
            return sports.description
        } else if let arts = item as? ArtsDanceContent {
            return arts.description
        }
        return "No description available"
    }
}
