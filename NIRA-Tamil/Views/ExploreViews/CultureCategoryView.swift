import SwiftUI

struct CultureCategoryView: View {
    let category: EnhancedCulturalCategory
    @StateObject private var cultureService = EnhancedCultureService.shared
    @Environment(\.dismiss) private var dismiss
    @State private var contentItems: [Any] = []

    var body: some View {
        ZStack {
            // Dark background like the cultural page
            Color.black
                .ignoresSafeArea()

            ScrollView {
                VStack(spacing: 24) {
                    // Header section with colorful gradient design
                    headerSection

                    // Content grid
                    contentGrid
                }
                .padding(.horizontal, 20)
                .padding(.top, 20)
                .padding(.bottom, 100)
            }
        }
        .navigationBarTitleDisplayMode(.inline)
        .navigationTitle(category.name)
        .toolbar {
            ToolbarItem(placement: .navigationBarTrailing) {
                Button("Done") {
                    dismiss()
                }
                .foregroundColor(.blue)
                .font(.system(size: 16, weight: .medium))
            }
        }
        .toolbarBackground(.clear, for: .navigationBar)
        .onAppear {
            loadContent()
        }
    }

    private var headerSection: some View {
        VStack(spacing: 20) {
            // Colorful gradient header card like the cultural page
            VStack(spacing: 16) {
                // Colorful progress bar at top
                RoundedRectangle(cornerRadius: 2)
                    .fill(
                        LinearGradient(
                            colors: [
                                Color.pink,
                                Color.purple,
                                Color.blue
                            ],
                            startPoint: .leading,
                            endPoint: .trailing
                        )
                    )
                    .frame(height: 4)
                    .padding(.horizontal, 24)

                // Category badge
                HStack {
                    Text(category.name)
                        .font(.system(size: 14, weight: .medium))
                        .foregroundColor(.white)
                        .padding(.horizontal, 12)
                        .padding(.vertical, 6)
                        .background(
                            Capsule()
                                .fill(Color.pink.opacity(0.8))
                        )
                    Spacer()
                }
                .padding(.horizontal, 24)

                // Main title
                VStack(alignment: .leading, spacing: 8) {
                    Text(getMainTitle())
                        .font(.system(size: 28, weight: .bold))
                        .foregroundColor(.white)
                        .multilineTextAlignment(.leading)

                    // Tamil subtitle
                    Text(category.nameTamil)
                        .font(.system(size: 16, weight: .medium))
                        .foregroundColor(.gray)
                }
                .frame(maxWidth: .infinity, alignment: .leading)
                .padding(.horizontal, 24)

                // Description
                if let description = category.description {
                    Text(description)
                        .font(.system(size: 16, weight: .regular))
                        .foregroundColor(.gray)
                        .multilineTextAlignment(.leading)
                        .frame(maxWidth: .infinity, alignment: .leading)
                        .padding(.horizontal, 24)
                }

                // Action buttons row
                HStack(spacing: 16) {
                    // Type badge
                    HStack(spacing: 6) {
                        Image(systemName: "tag.fill")
                            .font(.system(size: 12))
                            .foregroundColor(.white)
                        Text("Type")
                            .font(.system(size: 12, weight: .medium))
                            .foregroundColor(.gray)
                        Text(category.name)
                            .font(.system(size: 12, weight: .medium))
                            .foregroundColor(.white)
                    }
                    .padding(.horizontal, 12)
                    .padding(.vertical, 8)
                    .background(
                        RoundedRectangle(cornerRadius: 12)
                            .fill(Color.gray.opacity(0.2))
                    )

                    // Level badge
                    HStack(spacing: 6) {
                        Image(systemName: "star.fill")
                            .font(.system(size: 12))
                            .foregroundColor(.white)
                        Text("Level")
                            .font(.system(size: 12, weight: .medium))
                            .foregroundColor(.gray)
                        Text("Explore")
                            .font(.system(size: 12, weight: .medium))
                            .foregroundColor(.white)
                    }
                    .padding(.horizontal, 12)
                    .padding(.vertical, 8)
                    .background(
                        RoundedRectangle(cornerRadius: 12)
                            .fill(Color.gray.opacity(0.2))
                    )

                    Spacer()

                    // Learn button
                    Button(action: {}) {
                        HStack(spacing: 6) {
                            Text("Learn...")
                                .font(.system(size: 14, weight: .medium))
                                .foregroundColor(.white)
                            Image(systemName: "arrow.right")
                                .font(.system(size: 12, weight: .medium))
                                .foregroundColor(.white)
                        }
                        .padding(.horizontal, 16)
                        .padding(.vertical, 10)
                        .background(
                            RoundedRectangle(cornerRadius: 20)
                                .fill(
                                    LinearGradient(
                                        colors: [Color.pink, Color.purple],
                                        startPoint: .leading,
                                        endPoint: .trailing
                                    )
                                )
                        )
                    }
                }
                .padding(.horizontal, 24)
                .padding(.bottom, 8)
            }
            .background(
                RoundedRectangle(cornerRadius: 20)
                    .fill(
                        LinearGradient(
                            colors: [
                                Color(red: 0.4, green: 0.2, blue: 0.3),
                                Color(red: 0.3, green: 0.15, blue: 0.25)
                            ],
                            startPoint: .topLeading,
                            endPoint: .bottomTrailing
                        )
                    )
            )
        }
    }

    // MARK: - Content Grid
    private var contentGrid: some View {
        VStack(spacing: 16) {
            ForEach(Array(contentItems.enumerated()), id: \.offset) { index, item in
                contentCard(for: item, index: index)
            }
        }
    }

    // MARK: - Content Card
    @ViewBuilder
    private func contentCard(for item: Any, index: Int) -> some View {
        VStack(spacing: 16) {
            // Colorful progress bar at top
            RoundedRectangle(cornerRadius: 2)
                .fill(
                    LinearGradient(
                        colors: [
                            Color.pink,
                            Color.purple,
                            Color.blue
                        ],
                        startPoint: .leading,
                        endPoint: .trailing
                    )
                )
                .frame(height: 4)
                .padding(.horizontal, 24)

            // Category badge
            HStack {
                Text(category.name)
                    .font(.system(size: 12, weight: .medium))
                    .foregroundColor(.white)
                    .padding(.horizontal, 10)
                    .padding(.vertical, 4)
                    .background(
                        Capsule()
                            .fill(Color.pink.opacity(0.8))
                    )
                Spacer()
            }
            .padding(.horizontal, 24)

            // Content title and description
            VStack(alignment: .leading, spacing: 8) {
                Text(getItemTitle(item))
                    .font(.system(size: 24, weight: .bold))
                    .foregroundColor(.white)
                    .multilineTextAlignment(.leading)

                // Tamil subtitle if available
                if let tamilTitle = getTamilTitle(item) {
                    Text(tamilTitle)
                        .font(.system(size: 14, weight: .medium))
                        .foregroundColor(.gray)
                }

                Text(getItemDescription(item))
                    .font(.system(size: 16, weight: .regular))
                    .foregroundColor(.gray)
                    .multilineTextAlignment(.leading)
                    .lineLimit(3)
            }
            .frame(maxWidth: .infinity, alignment: .leading)
            .padding(.horizontal, 24)

            // Action buttons row
            HStack(spacing: 16) {
                // Type badge
                HStack(spacing: 6) {
                    Image(systemName: "tag.fill")
                        .font(.system(size: 10))
                        .foregroundColor(.white)
                    Text("Type")
                        .font(.system(size: 10, weight: .medium))
                        .foregroundColor(.gray)
                    Text(getItemType(item))
                        .font(.system(size: 10, weight: .medium))
                        .foregroundColor(.white)
                }
                .padding(.horizontal, 10)
                .padding(.vertical, 6)
                .background(
                    RoundedRectangle(cornerRadius: 10)
                        .fill(Color.gray.opacity(0.2))
                )

                // Level badge
                HStack(spacing: 6) {
                    Image(systemName: "star.fill")
                        .font(.system(size: 10))
                        .foregroundColor(.white)
                    Text("Level")
                        .font(.system(size: 10, weight: .medium))
                        .foregroundColor(.gray)
                    Text("Explore")
                        .font(.system(size: 10, weight: .medium))
                        .foregroundColor(.white)
                }
                .padding(.horizontal, 10)
                .padding(.vertical, 6)
                .background(
                    RoundedRectangle(cornerRadius: 10)
                        .fill(Color.gray.opacity(0.2))
                )

                Spacer()

                // Learn button
                Button(action: {
                    print("Tapped item: \(getItemTitle(item))")
                }) {
                    HStack(spacing: 4) {
                        Text("Learn...")
                            .font(.system(size: 12, weight: .medium))
                            .foregroundColor(.white)
                        Image(systemName: "arrow.right")
                            .font(.system(size: 10, weight: .medium))
                            .foregroundColor(.white)
                    }
                    .padding(.horizontal, 12)
                    .padding(.vertical, 8)
                    .background(
                        RoundedRectangle(cornerRadius: 16)
                            .fill(
                                LinearGradient(
                                    colors: [Color.pink, Color.purple],
                                    startPoint: .leading,
                                    endPoint: .trailing
                                )
                            )
                    )
                }
            }
            .padding(.horizontal, 24)
            .padding(.bottom, 8)
        }
        .background(
            RoundedRectangle(cornerRadius: 20)
                .fill(
                    LinearGradient(
                        colors: [
                            Color(red: 0.4, green: 0.2, blue: 0.3),
                            Color(red: 0.3, green: 0.15, blue: 0.25)
                        ],
                        startPoint: .topLeading,
                        endPoint: .bottomTrailing
                    )
                )
        )
    }

    // MARK: - Helper Methods
    private func loadContent() {
        contentItems = cultureService.getContent(for: category.name)
        print("📱 Loaded \(contentItems.count) items for \(category.name)")
    }

    private func getMainTitle() -> String {
        // Create a dynamic title based on the first content item or category
        if let firstItem = contentItems.first {
            return getItemTitle(firstItem)
        }
        return "\(category.name) - Cultural Heritage"
    }

    private func getTamilTitle(_ item: Any) -> String? {
        // Return Tamil title if available
        if let cinema = item as? CinemaContent {
            return cinema.titleTamil
        } else if let music = item as? MusicContent {
            return music.titleTamil
        } else if let festival = item as? FestivalsContent {
            return festival.titleTamil
        } else if let architecture = item as? ArchitectureContent {
            return architecture.titleTamil
        } else if let cuisine = item as? CuisineContent {
            return cuisine.titleTamil
        } else if let sports = item as? SportsContent {
            return sports.nameTamil
        } else if let arts = item as? ArtsDanceContent {
            return arts.titleTamil
        }
        return nil
    }

    private func getItemType(_ item: Any) -> String {
        if item is CinemaContent {
            return "Cinema"
        } else if item is MusicContent {
            return "Music"
        } else if item is FestivalsContent {
            return "Festival"
        } else if item is ArchitectureContent {
            return "Architecture"
        } else if item is CuisineContent {
            return "Cuisine"
        } else if item is SportsContent {
            return "Sports"
        } else if item is ArtsDanceContent {
            return "Arts"
        }
        return "Cultural"
    }

    private func getItemTitle(_ item: Any) -> String {
        if let cinema = item as? CinemaContent {
            return cinema.title
        } else if let music = item as? MusicContent {
            return music.title
        } else if let festival = item as? FestivalsContent {
            return festival.title
        } else if let architecture = item as? ArchitectureContent {
            return architecture.title
        } else if let cuisine = item as? CuisineContent {
            return cuisine.title
        } else if let sports = item as? SportsContent {
            return sports.name
        } else if let arts = item as? ArtsDanceContent {
            return arts.title
        }
        return "Unknown Item"
    }

    private func getItemDescription(_ item: Any) -> String {
        if let cinema = item as? CinemaContent {
            return cinema.description
        } else if let music = item as? MusicContent {
            return music.description
        } else if let festival = item as? FestivalsContent {
            return festival.description
        } else if let architecture = item as? ArchitectureContent {
            return architecture.description
        } else if let cuisine = item as? CuisineContent {
            return cuisine.description
        } else if let sports = item as? SportsContent {
            return sports.description
        } else if let arts = item as? ArtsDanceContent {
            return arts.description
        }
        return "No description available"
    }
}
