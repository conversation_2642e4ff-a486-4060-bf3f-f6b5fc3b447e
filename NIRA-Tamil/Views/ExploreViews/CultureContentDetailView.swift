import SwiftUI

// MARK: - Content Item Wrapper
struct AnyContentItem {
    let content: Any
    let title: String
    let description: String
    let type: String
    
    init(_ content: Any) {
        self.content = content
        
        if let arts = content as? ArtsDanceContent {
            self.title = arts.title
            self.description = arts.description
            self.type = "Arts & Dance"
        } else if let music = content as? MusicContent {
            self.title = music.title
            self.description = music.description
            self.type = "Music"
        } else if let festival = content as? FestivalsContent {
            self.title = festival.title
            self.description = festival.description
            self.type = "Festival"
        } else if let architecture = content as? ArchitectureContent {
            self.title = architecture.title
            self.description = architecture.description
            self.type = "Architecture"
        } else if let cuisine = content as? CuisineContent {
            self.title = cuisine.title
            self.description = cuisine.description
            self.type = "Cuisine"
        } else if let cinema = content as? CinemaContent {
            self.title = cinema.title
            self.description = cinema.description
            self.type = "Cinema"
        } else if let sports = content as? SportsContent {
            self.title = sports.name
            self.description = sports.description
            self.type = "Sports"
        } else {
            self.title = "Unknown"
            self.description = "Unknown content"
            self.type = "Unknown"
        }
    }
}

// MARK: - Detail View
struct CultureContentDetailView: View {
    let content: AnyContentItem
    @Environment(\.dismiss) private var dismiss
    
    var body: some View {
        ScrollView {
            VStack(alignment: .leading, spacing: 20) {
                // Header
                headerSection
                
                // Content based on type
                contentSection
                
                Spacer(minLength: 100)
            }
            .padding()
        }
        .background(
            LinearGradient(
                colors: [getColorForType().opacity(0.1), Color.clear],
                startPoint: .top,
                endPoint: .bottom
            )
        )
        .navigationTitle(content.title)
        .navigationBarTitleDisplayMode(.large)
        .toolbar {
            ToolbarItem(placement: .navigationBarTrailing) {
                Button("Done") {
                    dismiss()
                }
            }
        }
    }
    
    private var headerSection: some View {
        VStack(alignment: .leading, spacing: 16) {
            // Type Badge
            HStack {
                Text(content.type)
                    .font(.caption)
                    .fontWeight(.semibold)
                    .foregroundColor(.white)
                    .padding(.horizontal, 12)
                    .padding(.vertical, 6)
                    .background(getColorForType())
                    .clipShape(Capsule())
                
                Spacer()
            }
            
            // Title
            Text(content.title)
                .font(.largeTitle)
                .fontWeight(.bold)
                .lineLimit(3)
            
            // Description
            Text(content.description)
                .font(.body)
                .foregroundColor(.secondary)
                .lineLimit(nil)
        }
        .padding()
        .background(.ultraThinMaterial)
        .clipShape(RoundedRectangle(cornerRadius: 20))
    }
    
    private var contentSection: some View {
        VStack(alignment: .leading, spacing: 16) {
            if let arts = content.content as? ArtsDanceContent {
                artsDanceDetails(arts)
            } else if let music = content.content as? MusicContent {
                musicDetails(music)
            } else if let festival = content.content as? FestivalsContent {
                festivalDetails(festival)
            } else if let architecture = content.content as? ArchitectureContent {
                architectureDetails(architecture)
            } else if let cuisine = content.content as? CuisineContent {
                cuisineDetails(cuisine)
            } else if let cinema = content.content as? CinemaContent {
                cinemaDetails(cinema)
            } else if let sports = content.content as? SportsContent {
                sportsDetails(sports)
            }
        }
    }
    
    private func artsDanceDetails(_ content: ArtsDanceContent) -> some View {
        VStack(alignment: .leading, spacing: 16) {
            if let titleTamil = content.titleTamil {
                detailCard(title: "Tamil Title", content: titleTamil)
            }
            
            if let culturalImpact = content.culturalImpact {
                detailCard(title: "Cultural Impact", content: culturalImpact)
            }
            
            if let historicalSignificance = content.historicalSignificance {
                detailCard(title: "Historical Significance", content: historicalSignificance)
            }
            
            if let modernRelevance = content.modernRelevance {
                detailCard(title: "Modern Relevance", content: modernRelevance)
            }
            
            if let techniques = content.techniques, !techniques.isEmpty {
                listCard(title: "Techniques", items: techniques)
            }
            
            if let instruments = content.instrumentsUsed, !instruments.isEmpty {
                listCard(title: "Instruments Used", items: instruments)
            }
        }
    }
    
    private func musicDetails(_ content: MusicContent) -> some View {
        VStack(alignment: .leading, spacing: 16) {
            if let titleTamil = content.titleTamil {
                detailCard(title: "Tamil Title", content: titleTamil)
            }
            
            if let composer = content.composerArtist {
                detailCard(title: "Composer/Artist", content: composer)
            }
            
            if let ragaTala = content.ragaTalaInfo {
                detailCard(title: "Raga & Tala", content: ragaTala)
            }
            
            if let culturalImpact = content.culturalImpact {
                detailCard(title: "Cultural Impact", content: culturalImpact)
            }
            
            if let instruments = content.instrumentsFeatured, !instruments.isEmpty {
                listCard(title: "Instruments Featured", items: instruments)
            }
        }
    }
    
    // Placeholder detail methods for other content types
    private func festivalDetails(_ content: FestivalsContent) -> some View {
        VStack(alignment: .leading, spacing: 16) {
            detailCard(title: "Festival Details", content: "Detailed information about \(content.title)")
        }
    }
    
    private func architectureDetails(_ content: ArchitectureContent) -> some View {
        VStack(alignment: .leading, spacing: 16) {
            detailCard(title: "Architecture Details", content: "Detailed information about \(content.title)")
        }
    }
    
    private func cuisineDetails(_ content: CuisineContent) -> some View {
        VStack(alignment: .leading, spacing: 16) {
            detailCard(title: "Cuisine Details", content: "Detailed information about \(content.title)")
        }
    }
    
    private func cinemaDetails(_ content: CinemaContent) -> some View {
        VStack(alignment: .leading, spacing: 16) {
            detailCard(title: "Cinema Details", content: "Detailed information about \(content.title)")
        }
    }
    
    private func sportsDetails(_ content: SportsContent) -> some View {
        VStack(alignment: .leading, spacing: 16) {
            detailCard(title: "Sports Details", content: "Detailed information about \(content.name)")
        }
    }
    
    private func detailCard(title: String, content: String) -> some View {
        VStack(alignment: .leading, spacing: 8) {
            Text(title)
                .font(.headline)
                .fontWeight(.semibold)
            
            Text(content)
                .font(.body)
                .foregroundColor(.secondary)
        }
        .padding()
        .background(.ultraThinMaterial)
        .clipShape(RoundedRectangle(cornerRadius: 16))
    }
    
    private func listCard(title: String, items: [String]) -> some View {
        VStack(alignment: .leading, spacing: 8) {
            Text(title)
                .font(.headline)
                .fontWeight(.semibold)
            
            LazyVGrid(columns: [
                GridItem(.flexible()),
                GridItem(.flexible())
            ], spacing: 8) {
                ForEach(items, id: \.self) { item in
                    Text(item)
                        .font(.caption)
                        .padding(.horizontal, 8)
                        .padding(.vertical, 4)
                        .background(getColorForType().opacity(0.2))
                        .clipShape(Capsule())
                }
            }
        }
        .padding()
        .background(.ultraThinMaterial)
        .clipShape(RoundedRectangle(cornerRadius: 16))
    }
    
    private func getColorForType() -> Color {
        switch content.type {
        case "Arts & Dance": return .pink
        case "Music": return .purple
        case "Festival": return .orange
        case "Architecture": return .brown
        case "Cuisine": return .green
        case "Cinema": return .red
        case "Sports": return .blue
        default: return .gray
        }
    }
}

#Preview {
    NavigationView {
        CultureContentDetailView(content: AnyContentItem(
            ArtsDanceContent(
                id: UUID(),
                title: "Bharatanatyam",
                titleTamil: "பரதநாட்டியம்",
                contentType: "classical_dance",
                description: "Classical dance form",
                descriptionTamil: nil,
                culturalImpact: "Significant cultural impact",
                historicalSignificance: "Ancient origins",
                modernRelevance: "Still practiced today",
                artFormType: "classical",
                originRegion: "Tamil Nadu",
                keyPractitioners: ["Artist 1", "Artist 2"],
                techniques: ["Technique 1", "Technique 2"],
                instrumentsUsed: ["Mridangam", "Violin"],
                costumeDetails: "Traditional costume",
                performanceContext: "Temple performances",
                learningResources: ["Resource 1"],
                audioUrl: nil,
                imageUrl: nil,
                videoUrl: nil,
                romanization: "Bharatanatyam",
                tags: ["classical", "dance"],
                difficultyLevel: "intermediate",
                isFeatured: true
            )
        ))
    }
}
