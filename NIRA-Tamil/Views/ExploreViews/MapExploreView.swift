import SwiftUI
import MapKit

struct MapExploreView: View {
    @StateObject private var mapService = CulturalMapService.shared
    @State private var selectedLocation: CulturalLocation?
    @State private var showingLocationDetail = false
    @State private var mapRegion = MKCoordinateRegion(
        center: CLLocationCoordinate2D(latitude: 11.1271, longitude: 78.6569), // Tamil Nadu center
        span: MKCoordinateSpan(latitudeDelta: 3.0, longitudeDelta: 3.0)
    )

    var body: some View {
        ScrollView {
            LazyVStack(spacing: 20) {
                // Header section
                VStack(alignment: .leading, spacing: 8) {
                    Text("Cultural Map")
                        .font(.title)
                        .fontWeight(.bold)
                        .padding(.horizontal)

                    Text("Explore Tamil heritage sites with Apple Maps")
                        .font(.subheadline)
                        .foregroundColor(.secondary)
                        .padding(.horizontal)
                }
                .padding(.top)

                // Interactive Apple Map
                modernInteractiveMap

                // Location Type Filter
                locationTypeFilter

                // Cultural Locations
                VStack(alignment: .leading, spacing: 12) {
                    HStack {
                        Text("Cultural Locations")
                            .font(.headline)
                            .fontWeight(.semibold)

                        Spacer()

                        Text("\(mapService.filteredLocations.count) places")
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }
                    .padding(.horizontal)

                    ForEach(mapService.filteredLocations.prefix(5), id: \.id) { location in
                        modernLocationCard(location: location)
                    }

                    if mapService.filteredLocations.count > 5 {
                        Button("View All Locations") {
                            // Navigate to full list
                        }
                        .font(.subheadline)
                        .fontWeight(.medium)
                        .foregroundColor(.blue)
                        .padding(.horizontal)
                    }
                }
            }
            .padding(.bottom, 100)
        }
        .task {
            await mapService.loadLocations()
        }
        .sheet(isPresented: $showingLocationDetail) {
            if let location = selectedLocation {
                LocationDetailView(location: location)
            }
        }
    }
    
    // MARK: - Interactive Apple Map

    private var modernInteractiveMap: some View {
        VStack(spacing: 12) {
            // Map container with glassmorphic design
            ZStack {
                // Animated glowing orb background
                Circle()
                    .fill(mapGradient)
                    .frame(width: 120, height: 120)
                    .blur(radius: 30)
                    .opacity(0.3)
                    .offset(x: 50, y: -50)

                VStack(spacing: 16) {
                    // Top gradient stripe
                    Rectangle()
                        .fill(mapGradient)
                        .frame(height: 6)
                        .clipShape(RoundedRectangle(cornerRadius: 3))

                    // Apple Map View
                    Map(coordinateRegion: $mapRegion, annotationItems: mapService.filteredLocations) { location in
                        MapAnnotation(coordinate: CLLocationCoordinate2D(
                            latitude: location.latitude,
                            longitude: location.longitude
                        )) {
                            Button(action: {
                                selectedLocation = location
                                showingLocationDetail = true
                            }) {
                                VStack(spacing: 4) {
                                    // Custom map pin with location type icon
                                    ZStack {
                                        Circle()
                                            .fill(locationTypeGradient(for: location.locationType))
                                            .frame(width: 32, height: 32)
                                            .shadow(color: .black.opacity(0.3), radius: 4, x: 0, y: 2)

                                        Image(systemName: locationTypeIcon(for: location.locationType))
                                            .font(.system(size: 14, weight: .bold))
                                            .foregroundColor(.white)
                                    }

                                    // Location name
                                    Text(location.name)
                                        .font(.caption2)
                                        .fontWeight(.medium)
                                        .foregroundColor(.primary)
                                        .padding(.horizontal, 6)
                                        .padding(.vertical, 2)
                                        .background(Color.white.opacity(0.9))
                                        .clipShape(Capsule())
                                        .shadow(color: .black.opacity(0.2), radius: 2, x: 0, y: 1)
                                }
                            }
                        }
                    }
                    .frame(height: 250)
                    .clipShape(RoundedRectangle(cornerRadius: 12))
                    .overlay(
                        RoundedRectangle(cornerRadius: 12)
                            .stroke(Color.gray.opacity(0.3), lineWidth: 1)
                    )

                    // Map controls
                    HStack {
                        Button("Center on Tamil Nadu") {
                            withAnimation {
                                mapRegion = MKCoordinateRegion(
                                    center: CLLocationCoordinate2D(latitude: 11.1271, longitude: 78.6569),
                                    span: MKCoordinateSpan(latitudeDelta: 3.0, longitudeDelta: 3.0)
                                )
                            }
                        }
                        .font(.caption)
                        .foregroundColor(.blue)

                        Spacer()

                        Button("Open in Apple Maps") {
                            openInAppleMaps()
                        }
                        .font(.caption)
                        .fontWeight(.medium)
                        .foregroundColor(.white)
                        .padding(.horizontal, 12)
                        .padding(.vertical, 6)
                        .background(mapGradient)
                        .clipShape(Capsule())
                    }
                }
                .padding(20)
            }
        }
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(Color.systemBackground)
                .shadow(color: Color.gray.opacity(0.2), radius: 8, x: 0, y: 4)
        )
        .clipShape(RoundedRectangle(cornerRadius: 16))
        .padding(.horizontal)
    }

    // MARK: - Location Type Filter

    private var locationTypeFilter: some View {
        ScrollView(.horizontal, showsIndicators: false) {
            HStack(spacing: 12) {
                ForEach(["All", "Temple", "Museum", "Heritage Site", "Monument"], id: \.self) { type in
                    locationTypeButton(type: type)
                }
            }
            .padding(.horizontal)
        }
    }

    private func locationTypeButton(type: String) -> some View {
        Button(action: {
            mapService.selectedLocationTypeFilter = type
        }) {
            HStack(spacing: 6) {
                Image(systemName: locationTypeIcon(for: type.lowercased().replacingOccurrences(of: " ", with: "_")))
                    .font(.caption)

                Text(type)
                    .font(.caption)
                    .fontWeight(.medium)
            }
            .padding(.horizontal, 12)
            .padding(.vertical, 6)
            .background(
                RoundedRectangle(cornerRadius: 12)
                    .fill(mapService.selectedLocationTypeFilter == type ?
                          AnyShapeStyle(mapGradient) : AnyShapeStyle(Color.gray.opacity(0.2)))
            )
            .foregroundColor(mapService.selectedLocationTypeFilter == type ?
                           .white : .primary)
        }
    }

    // MARK: - Quick Actions Section



    private var mapGradient: LinearGradient {
        LinearGradient(colors: [.pink, .purple], startPoint: .topLeading, endPoint: .bottomTrailing)
    }

    // MARK: - Modern Location Card

    private func modernLocationCard(location: CulturalLocation) -> some View {
        Button(action: {
            selectedLocation = location
            showingLocationDetail = true
        }) {
            ZStack {
                // Animated glowing orb background
                Circle()
                    .fill(locationTypeGradient(for: location.locationType))
                    .frame(width: 80, height: 80)
                    .blur(radius: 20)
                    .opacity(0.3)
                    .offset(x: 40, y: -40)

                VStack(alignment: .leading, spacing: 16) {
                    // Top gradient stripe
                    Rectangle()
                        .fill(locationTypeGradient(for: location.locationType))
                        .frame(height: 4)
                        .clipShape(RoundedRectangle(cornerRadius: 2))

                    HStack(spacing: 16) {
                        // Location icon with gradient background
                        ZStack {
                            RoundedRectangle(cornerRadius: 12)
                                .fill(locationTypeGradient(for: location.locationType))
                                .frame(width: 50, height: 50)

                            Image(systemName: locationTypeIcon(for: location.locationType))
                                .font(.title3)
                                .foregroundColor(.white)
                        }

                        // Location details
                        VStack(alignment: .leading, spacing: 4) {
                            Text(location.name)
                                .font(.subheadline)
                                .fontWeight(.bold)
                                .foregroundColor(.primary)
                                .lineLimit(1)

                            Text(location.nameTamil)
                                .font(.caption)
                                .foregroundColor(.secondary)
                                .lineLimit(1)

                            Text(location.city)
                                .font(.caption)
                                .foregroundColor(.secondary)

                            Text(location.description)
                                .font(.caption2)
                                .foregroundColor(.secondary)
                                .lineLimit(2)
                        }

                        Spacer()

                        // Action buttons
                        VStack(spacing: 8) {
                            Button(action: {
                                openLocationInAppleMaps(location: location)
                            }) {
                                Image(systemName: "location.fill")
                                    .font(.caption)
                                    .foregroundColor(.white)
                                    .frame(width: 28, height: 28)
                                    .background(locationTypeGradient(for: location.locationType))
                                    .clipShape(Circle())
                            }

                            if location.audioGuideUrl != nil {
                                Button(action: {
                                    // Play audio guide
                                }) {
                                    Image(systemName: "speaker.wave.2.fill")
                                        .font(.caption)
                                        .foregroundColor(.white)
                                        .frame(width: 28, height: 28)
                                        .background(locationTypeGradient(for: location.locationType))
                                        .clipShape(Circle())
                                }
                            }
                        }
                    }
                }
                .padding(16)
            }
        }
        .buttonStyle(PlainButtonStyle())
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(Color.systemBackground)
                .shadow(color: Color.gray.opacity(0.2), radius: 8, x: 0, y: 4)
        )
        .clipShape(RoundedRectangle(cornerRadius: 16))
        .padding(.horizontal)
    }

    // MARK: - Helper Functions

    private func locationTypeGradient(for locationType: String) -> LinearGradient {
        switch locationType.lowercased() {
        case "temple": return LinearGradient(colors: [.orange, .red], startPoint: .topLeading, endPoint: .bottomTrailing)
        case "museum": return LinearGradient(colors: [.blue, .cyan], startPoint: .topLeading, endPoint: .bottomTrailing)
        case "heritage_site", "historical_site": return LinearGradient(colors: [.purple, .pink], startPoint: .topLeading, endPoint: .bottomTrailing)
        case "monument": return LinearGradient(colors: [.brown, .orange], startPoint: .topLeading, endPoint: .bottomTrailing)
        case "cultural_center": return LinearGradient(colors: [.green, .mint], startPoint: .topLeading, endPoint: .bottomTrailing)
        default: return LinearGradient(colors: [.pink, .purple], startPoint: .topLeading, endPoint: .bottomTrailing)
        }
    }

    private func locationTypeIcon(for locationType: String) -> String {
        switch locationType.lowercased() {
        case "temple": return "building.columns.fill"
        case "museum": return "building.fill"
        case "heritage_site", "historical_site": return "crown.fill"
        case "monument": return "flag.fill"
        case "cultural_center": return "theatermasks.fill"
        case "all": return "map.fill"
        default: return "mappin.circle.fill"
        }
    }

    private func openInAppleMaps() {
        let coordinate = CLLocationCoordinate2D(latitude: 11.1271, longitude: 78.6569)
        let mapItem = MKMapItem(placemark: MKPlacemark(coordinate: coordinate))
        mapItem.name = "Tamil Nadu Cultural Sites"
        mapItem.openInMaps(launchOptions: [MKLaunchOptionsMapTypeKey: MKMapType.standard.rawValue])
    }

    private func openLocationInAppleMaps(location: CulturalLocation) {
        let coordinate = CLLocationCoordinate2D(latitude: location.latitude, longitude: location.longitude)
        let mapItem = MKMapItem(placemark: MKPlacemark(coordinate: coordinate))
        mapItem.name = location.name
        mapItem.openInMaps(launchOptions: [
            MKLaunchOptionsMapTypeKey: MKMapType.standard.rawValue,
            MKLaunchOptionsDirectionsModeKey: MKLaunchOptionsDirectionsModeDriving
        ])
    }
}

// MARK: - Location Detail View

struct LocationDetailView: View {
    let location: CulturalLocation
    @Environment(\.dismiss) private var dismiss

    var body: some View {
        NavigationView {
            ScrollView {
                VStack(alignment: .leading, spacing: 20) {
                    // Header with image placeholder
                    ZStack {
                        Rectangle()
                            .fill(LinearGradient(colors: [.blue, .purple], startPoint: .topLeading, endPoint: .bottomTrailing))
                            .frame(height: 200)

                        VStack {
                            Image(systemName: "building.columns.fill")
                                .font(.system(size: 48))
                                .foregroundColor(.white)

                            Text(location.name)
                                .font(.title2)
                                .fontWeight(.bold)
                                .foregroundColor(.white)
                                .multilineTextAlignment(.center)
                        }
                    }

                    VStack(alignment: .leading, spacing: 16) {
                        // Tamil name
                        Text(location.nameTamil)
                            .font(.title3)
                            .fontWeight(.semibold)
                            .foregroundColor(.primary)

                        // Description
                        Text(location.description)
                            .font(.body)
                            .foregroundColor(.secondary)

                        // Historical significance
                        if let significance = location.historicalSignificance {
                            VStack(alignment: .leading, spacing: 8) {
                                Text("Historical Significance")
                                    .font(.headline)
                                    .fontWeight(.semibold)

                                Text(significance)
                                    .font(.body)
                                    .foregroundColor(.secondary)
                            }
                        }

                        // Quick actions
                        HStack(spacing: 12) {
                            Button("Get Directions") {
                                // Open in Apple Maps
                            }
                            .padding()
                            .background(Color.blue)
                            .foregroundColor(.white)
                            .clipShape(RoundedRectangle(cornerRadius: 12))

                            if location.audioGuideUrl != nil {
                                Button("Audio Guide") {
                                    // Play audio guide
                                }
                                .padding()
                                .background(Color.green)
                                .foregroundColor(.white)
                                .clipShape(RoundedRectangle(cornerRadius: 12))
                            }
                        }
                    }
                    .padding()
                }
            }
            .navigationTitle("Location Details")
            .navigationBarTitleDisplayMode(.inline)
            .navigationBarItems(trailing: Button("Done") { dismiss() })
        }
    }
}

#Preview {
    MapExploreView()
}
