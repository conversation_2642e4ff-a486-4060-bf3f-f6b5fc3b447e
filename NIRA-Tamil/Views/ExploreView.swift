import SwiftUI

// MARK: - Cultural Explore Tab Enum

enum CulturalExploreTab: Int, CaseIterable {
    case literature = 0
    case culture = 1
    case places = 2

    var displayName: String {
        switch self {
        case .literature: return "Literature"
        case .culture: return "Culture"
        case .places: return "Places"
        }
    }

    var icon: String {
        switch self {
        case .literature: return "book.fill"
        case .culture: return "building.columns.fill"
        case .places: return "location.fill"
        }
    }
}

struct ExploreView: View {
    @State private var selectedTab = 0
    @State private var isLoading = false
    @State private var errorMessage: String?

    // Screen size detection for optimization
    @Environment(\.horizontalSizeClass) var horizontalSizeClass
    @Environment(\.verticalSizeClass) var verticalSizeClass

    private var isCompactScreen: Bool {
        horizontalSizeClass == .compact || verticalSizeClass == .compact
    }

    var body: some View {
        NavigationView {
            VStack(spacing: 0) {
                // Clean Header matching Lessons style - optimized for small screens
                HStack {
                    VStack(alignment: .leading, spacing: isCompactScreen ? 1 : 2) {
                        Text("Explore Tamil")
                            .font(.system(size: isCompactScreen ? 20 : 24, weight: .bold, design: .rounded))
                            .foregroundColor(.primary)
                        Text("Discover culture, heritage & traditions")
                            .font(isCompactScreen ? .caption2 : .caption)
                            .foregroundColor(.secondary)
                    }

                    Spacer()

                    // Tamil cultural symbol
                    Text("🏛️")
                        .font(isCompactScreen ? .title3 : .title2)
                }
                .padding(.horizontal, 16)
                .padding(.vertical, isCompactScreen ? 6 : 8)

                // Modern tab navigation matching Lessons design
                modernTabNavigation

                // Error handling
                if let errorMessage = errorMessage {
                    errorView(message: errorMessage)
                } else {
                    // Content based on selected tab with loading states
                    ZStack {
                        TabView(selection: $selectedTab) {
                            // Literature detailed page
                            LiteratureExploreView()
                                .tag(0)

                            // Cultural Insights detailed page
                            CultureExploreView()
                                .tag(1)

                            // Cultural Places
                            MapExploreView()
                                .tag(2)
                        }
                        .tabViewStyle(PageTabViewStyle(indexDisplayMode: .never))

                        // Loading overlay
                        if isLoading {
                            loadingView
                        }
                    }
                }
            }
            .navigationBarHidden(true)
            .onAppear {
                // Simulate loading state for demonstration
                loadContent()
            }
        }
        .navigationViewStyle(StackNavigationViewStyle()) // Better for small screens
    }

    // MARK: - Loading Content

    private func loadContent() {
        isLoading = true
        errorMessage = nil

        // Simulate loading delay
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) {
            isLoading = false
        }
    }

    // MARK: - Loading View

    private var loadingView: some View {
        ZStack {
            Color.black.opacity(0.1)
                .ignoresSafeArea()

            VStack(spacing: 16) {
                // Glassmorphic loading card
                VStack(spacing: 12) {
                    ProgressView()
                        .scaleEffect(1.2)
                        .progressViewStyle(CircularProgressViewStyle(tint: .blue))

                    Text("Loading Tamil Culture...")
                        .font(.subheadline)
                        .fontWeight(.medium)
                        .foregroundColor(.primary)
                }
                .padding(24)
                .background(
                    RoundedRectangle(cornerRadius: 16)
                        .fill(Color.systemBackground.opacity(0.9))
                        .shadow(color: Color.gray.opacity(0.3), radius: 10, x: 0, y: 5)
                )
            }
        }
    }

    // MARK: - Error View

    private func errorView(message: String) -> some View {
        VStack(spacing: 16) {
            Image(systemName: "exclamationmark.triangle.fill")
                .font(.system(size: 48))
                .foregroundColor(.orange)

            Text("Something went wrong")
                .font(.headline)
                .fontWeight(.semibold)

            Text(message)
                .font(.subheadline)
                .foregroundColor(.secondary)
                .multilineTextAlignment(.center)
                .padding(.horizontal)

            Button("Try Again") {
                loadContent()
            }
            .padding(.horizontal, 24)
            .padding(.vertical, 12)
            .background(
                RoundedRectangle(cornerRadius: 12)
                    .fill(LinearGradient(colors: [.blue, .purple], startPoint: .leading, endPoint: .trailing))
            )
            .foregroundColor(.white)
            .fontWeight(.medium)
        }
        .padding()
    }

    // MARK: - Modern Tab Navigation

    private var modernTabNavigation: some View {
        ScrollView(.horizontal, showsIndicators: false) {
            HStack(spacing: isCompactScreen ? 8 : 12) {
                ForEach(CulturalExploreTab.allCases, id: \.self) { tab in
                    modernTabButton(for: tab)
                }
            }
            .padding(.horizontal, 16)
        }
        .padding(.vertical, isCompactScreen ? 8 : 12)
    }

    private func modernTabButton(for tab: CulturalExploreTab) -> some View {
        Button(action: {
            withAnimation(.spring(response: 0.5, dampingFraction: 0.7)) {
                selectedTab = tab.rawValue
            }
        }) {
            HStack(spacing: isCompactScreen ? 6 : 8) {
                Image(systemName: tab.icon)
                    .font(.system(size: isCompactScreen ? 12 : 14, weight: .medium))
                    .foregroundColor(selectedTab == tab.rawValue ? .white : .secondary)

                if !isCompactScreen || selectedTab == tab.rawValue {
                    Text(tab.displayName)
                        .font(.system(size: isCompactScreen ? 12 : 14, weight: .medium))
                        .foregroundColor(selectedTab == tab.rawValue ? .white : .secondary)
                        .lineLimit(1)
                }
            }
            .padding(.horizontal, isCompactScreen ? 12 : 16)
            .padding(.vertical, isCompactScreen ? 8 : 10)
            .background(
                Group {
                    if selectedTab == tab.rawValue {
                        RoundedRectangle(cornerRadius: isCompactScreen ? 16 : 20)
                            .fill(
                                LinearGradient(
                                    colors: [tabGradientColors(for: tab).0, tabGradientColors(for: tab).1],
                                    startPoint: .leading,
                                    endPoint: .trailing
                                )
                            )
                            .shadow(color: tabGradientColors(for: tab).0.opacity(0.3), radius: 8, x: 0, y: 4)
                    } else {
                        RoundedRectangle(cornerRadius: isCompactScreen ? 16 : 20)
                            .fill(Color.gray.opacity(0.15))
                    }
                }
            )
        }
        .buttonStyle(PlainButtonStyle())
        .scaleEffect(selectedTab == tab.rawValue ? 1.05 : 1.0)
        .animation(.spring(response: 0.3, dampingFraction: 0.8), value: selectedTab)
    }

    // MARK: - Tab Gradient Colors

    private func tabGradientColors(for tab: CulturalExploreTab) -> (Color, Color) {
        switch tab {
        case .literature:
            return (.blue, .cyan)
        case .culture:
            return (.purple, .pink)
        case .places:
            return (.orange, .yellow)
        }
    }
}



// MARK: - Preview

#Preview {
    ExploreView()
}
