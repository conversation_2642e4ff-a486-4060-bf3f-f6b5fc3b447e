//
//  AudioManagementView.swift
//  NIRA-Tamil
//
//  Created by AI Assistant on 2024-12-19.
//

import SwiftUI

struct AudioManagementView: View {
    @StateObject private var audioService = AudioFileManagementService.shared
    @State private var showingCacheAlert = false
    @State private var testText = "வணக்கம், நீங்கள் எப்படி இருக்கிறீர்கள்?"
    
    var body: some View {
        NavigationView {
            ScrollView {
                VStack(spacing: 20) {
                    // Header
                    VStack(spacing: 8) {
                        Image(systemName: "speaker.wave.3.fill")
                            .font(.system(size: 50))
                            .foregroundColor(.niraPrimary)
                        
                        Text("Audio Management")
                            .font(.title2)
                            .fontWeight(.bold)
                        
                        Text("Manage audio files and TTS generation")
                            .font(.subheadline)
                            .foregroundColor(.secondary)
                            .multilineTextAlignment(.center)
                    }
                    .padding()
                    
                    // Cache Information
                    CacheInfoCard(
                        cacheSize: audioService.getCacheSize(),
                        cachedFilesCount: audioService.cachedAudioFiles.count
                    )
                    
                    // TTS Testing
                    VStack(alignment: .leading, spacing: 12) {
                        Text("Text-to-Speech Testing")
                            .font(.headline)
                            .padding(.horizontal)
                        
                        VStack(spacing: 12) {
                            TextField("Enter Tamil text", text: $testText)
                                .textFieldStyle(RoundedBorderTextFieldStyle())
                                .padding(.horizontal)
                            
                            HStack(spacing: 12) {
                                Button(action: {
                                    Task {
                                        await audioService.playAudio(text: testText, language: "ta")
                                    }
                                }) {
                                    HStack {
                                        Image(systemName: "play.circle.fill")
                                        Text("Play TTS")
                                    }
                                    .foregroundColor(.white)
                                    .padding()
                                    .background(Color.niraPrimary)
                                    .cornerRadius(8)
                                }
                                
                                Button(action: {
                                    audioService.stopAudio()
                                }) {
                                    HStack {
                                        Image(systemName: "stop.circle.fill")
                                        Text("Stop")
                                    }
                                    .foregroundColor(.white)
                                    .padding()
                                    .background(Color.red)
                                    .cornerRadius(8)
                                }
                            }
                            .padding(.horizontal)
                        }
                        .padding()
                        .background(Color(.systemGray6))
                        .cornerRadius(12)
                        .padding(.horizontal)
                    }
                    
                    // Audio Actions
                    VStack(alignment: .leading, spacing: 12) {
                        Text("Audio Actions")
                            .font(.headline)
                            .padding(.horizontal)
                        
                        LazyVGrid(columns: Array(repeating: GridItem(.flexible()), count: 2), spacing: 12) {
                            AudioActionButton(
                                title: "Generate Vocabulary Audio",
                                icon: "waveform.circle.fill",
                                color: .blue,
                                action: {
                                    Task {
                                        await generateSampleVocabularyAudio()
                                    }
                                }
                            )
                            
                            AudioActionButton(
                                title: "Preload Lesson Audio",
                                icon: "arrow.down.circle.fill",
                                color: .green,
                                action: {
                                    Task {
                                        await preloadSampleAudio()
                                    }
                                }
                            )
                            
                            AudioActionButton(
                                title: "Clear Cache",
                                icon: "trash.circle.fill",
                                color: .red,
                                action: {
                                    showingCacheAlert = true
                                }
                            )
                            
                            AudioActionButton(
                                title: "Test Playback",
                                icon: "play.circle.fill",
                                color: .orange,
                                action: {
                                    Task {
                                        await testAudioPlayback()
                                    }
                                }
                            )
                        }
                        .padding(.horizontal)
                    }
                    
                    // Download Progress
                    if !audioService.downloadProgress.isEmpty {
                        VStack(alignment: .leading, spacing: 12) {
                            Text("Download Progress")
                                .font(.headline)
                                .padding(.horizontal)
                            
                            ForEach(Array(audioService.downloadProgress.keys), id: \.self) { key in
                                ProgressCard(
                                    title: key.capitalized,
                                    progress: audioService.downloadProgress[key] ?? 0.0
                                )
                            }
                            .padding(.horizontal)
                        }
                    }
                    
                    // Error Display
                    if let errorMessage = audioService.errorMessage {
                        ErrorCard(message: errorMessage) {
                            audioService.errorMessage = nil
                        }
                    }
                    
                    Spacer(minLength: 50)
                }
            }
            .navigationTitle("Audio")
            .navigationBarTitleDisplayMode(.inline)
        }
        .alert("Clear Audio Cache", isPresented: $showingCacheAlert) {
            Button("Cancel", role: .cancel) { }
            Button("Clear", role: .destructive) {
                audioService.clearAudioCache()
            }
        } message: {
            Text("This will delete all cached audio files. They will be re-downloaded when needed.")
        }
    }
    
    // MARK: - Helper Methods
    
    private func generateSampleVocabularyAudio() async {
        let sampleVocabulary = [
            TamilSupabaseVocabulary(
                id: UUID(),
                lessonId: UUID(),
                vocabId: "1",
                englishWord: "Hello",
                tamilTranslation: "வணக்கம்",
                romanization: "Vaṇakkam",
                ipaPronunciation: "ʋaɳakkam",
                partOfSpeech: "Interjection",
                difficultyLevel: 1,
                frequencyRank: 1,
                culturalNotes: "Universal Tamil greeting",
                relatedTerms: ["greeting"],
                exampleSentenceEnglish: "Hello, how are you?",
                exampleSentenceTamil: "வணக்கம், நீங்கள் எப்படி இருக்கிறீர்கள்?",
                exampleSentenceRomanization: "Vaṇakkam, nīṅkaḷ eppaṭi irukkiṟīrkaḷ?",
                audioWordUrl: nil,
                audioSentenceUrl: nil,
                imageUrl: nil,
                createdAt: Date()
            )
        ]

        await audioService.generateVocabularyAudio(vocabulary: sampleVocabulary)
    }
    
    private func preloadSampleAudio() async {
        let sampleUrls = [
            "lesson_01_vocab_01_word.mp3",
            "lesson_01_vocab_02_word.mp3",
            "lesson_01_conv_01_line_01.mp3"
        ]
        
        await audioService.preloadLessonAudio(lessonId: "a1-lesson-1", audioUrls: sampleUrls)
    }
    
    private func testAudioPlayback() async {
        await audioService.playVocabularyAudio(vocabularyId: "sample-1", type: .tamil)
    }
}

struct CacheInfoCard: View {
    let cacheSize: Double
    let cachedFilesCount: Int
    
    var body: some View {
        VStack(spacing: 12) {
            HStack {
                VStack(alignment: .leading, spacing: 4) {
                    Text("Cache Size")
                        .font(.subheadline)
                        .foregroundColor(.secondary)
                    
                    Text(String(format: "%.1f MB", cacheSize))
                        .font(.title2)
                        .fontWeight(.semibold)
                }
                
                Spacer()
                
                VStack(alignment: .trailing, spacing: 4) {
                    Text("Cached Files")
                        .font(.subheadline)
                        .foregroundColor(.secondary)
                    
                    Text("\(cachedFilesCount)")
                        .font(.title2)
                        .fontWeight(.semibold)
                }
            }
            
            HStack {
                Image(systemName: "internaldrive")
                    .foregroundColor(.niraPrimary)
                
                Text("Audio files are cached locally for offline playback")
                    .font(.caption)
                    .foregroundColor(.secondary)
                
                Spacer()
            }
        }
        .padding()
        .background(Color(.systemGray6))
        .cornerRadius(12)
        .padding(.horizontal)
    }
}

struct AudioActionButton: View {
    let title: String
    let icon: String
    let color: Color
    let action: () -> Void
    
    var body: some View {
        Button(action: action) {
            VStack(spacing: 8) {
                Image(systemName: icon)
                    .font(.title2)
                    .foregroundColor(color)
                
                Text(title)
                    .font(.caption)
                    .fontWeight(.medium)
                    .multilineTextAlignment(.center)
                    .foregroundColor(.primary)
            }
            .frame(maxWidth: .infinity)
            .padding()
            .background(Color(.systemGray6))
            .cornerRadius(8)
        }
    }
}

struct ProgressCard: View {
    let title: String
    let progress: Double
    
    var body: some View {
        VStack(alignment: .leading, spacing: 8) {
            HStack {
                Text(title)
                    .font(.subheadline)
                    .fontWeight(.medium)
                
                Spacer()
                
                Text("\(Int(progress * 100))%")
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
            
            ProgressView(value: progress)
                .progressViewStyle(LinearProgressViewStyle(tint: .niraPrimary))
        }
        .padding()
        .background(Color(.systemGray6))
        .cornerRadius(8)
    }
}

struct ErrorCard: View {
    let message: String
    let onDismiss: () -> Void
    
    var body: some View {
        HStack {
            Image(systemName: "exclamationmark.triangle.fill")
                .foregroundColor(.red)
            
            Text(message)
                .font(.subheadline)
                .foregroundColor(.red)
            
            Spacer()
            
            Button("Dismiss", action: onDismiss)
                .font(.caption)
                .foregroundColor(.red)
        }
        .padding()
        .background(Color.red.opacity(0.1))
        .cornerRadius(8)
        .padding(.horizontal)
    }
}

#Preview {
    AudioManagementView()
}
